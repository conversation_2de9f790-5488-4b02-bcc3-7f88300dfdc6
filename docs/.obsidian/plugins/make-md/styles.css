body{--mk-ui-divider: var(--divider-color);--mk-ui-border: var(--background-modifier-border);--mk-ui-border-accent: var(--divider-color-hover);--mk-ui-radius-small: var(--radius-s);--mk-ui-radius-medium: var(--radius-m);--mk-ui-radius-large: var(--radius-l);--mk-ui-background-overlay: rgba(0, 0, 0, .5);--mk-ui-border-overlay: rgba(15, 15, 15, .5);--mk-ui-background-blur: var(--mk-ui-background);--mk-ui-border-hover: var(--background-modifier-border-hover);--mk-ui-handle-color: rgba(70, 79, 200, 1);--mk-ui-handle-color-hover: #eb3b5a22;--mk-ui-handle-outline: rgba(255, 255, 255, .8);--mk-ui-handle-fill: #fff;--mk-ui-active: var(--interactive-accent);--mk-ui-active-hover: var(--interactive-hover);--mk-ui-active-normal: var(--interactive-normal);--mk-ui-background: var(--background-primary);--mk-ui-background-variant: var(--background-secondary);--mk-ui-background-contrast: var(--background-primary-alt);--mk-ui-background-active: var(--background-modifier-active);--mk-ui-background-selected: var(--background-modifier-active-hover);--mk-ui-background-reverse: var(--text-normal);--mk-ui-background-hover: var(--background-modifier-hover);--mk-ui-background-menu: var(--background-secondary);--mk-ui-background-menu-input: var(--background-modifier-form-field);--mk-ui-background-menu-hover: var(--background-modifier-hover);--mk-ui-background-input: var(--background-modifier-form-field);--mk-ui-text-primary: var(--text-normal);--mk-ui-text-secondary: var(--text-muted);--mk-ui-text-tertiary: var(--text-faint);--mk-ui-text-accent: var(--text-on-accent);--mk-ui-text-reverse: var(--background-primary);--mk-ui-text-error: var(--mk-color-red);--mk-shadow-card: 0px 1px 4px 0px rgba(0, 0, 0, .4);--mk-shadow-menu: 0px 0px 32px 0px rgba(0, 0, 0, .2);--mk-color-none: rgba(var(--mono-rgb-100), .025);--mk-color-light-yellow: #FFF9C4;--mk-color-light-blue: #E3F2FD;--mk-color-light-red: #FFEBEE;--mk-color-light-green: #E8F5E8;--mk-color-light-purple: #F3E5F5;--mk-color-light-gray: #F5F5F5;--mk-color-black: #000000;--mk-color-white: #ffffff;--mk-color-base-0: var(--color-base-00);--mk-color-base-10: var(--color-base-10);--mk-color-base-20: var(--color-base-20);--mk-color-base-30: var(--color-base-30);--mk-color-base-40: var(--color-base-40);--mk-color-base-50: var(--color-base-50);--mk-color-base-60: var(--color-base-60);--mk-color-base-70: var(--color-base-70);--mk-color-base-100: var(--color-base-100);--mk-color-selection: var(--mk-ui-background-selected);--mk-color-ui-accent: var(--text-accent);--mk-layer-editor-popover: 100;--mk-layer-editor-overlay: 100}.mk-smooth-border{--b: 5px;--c: red;--w: 20px;border:var(--b) solid #0000;--_g: #0000 90deg,var(--c) 0;--_p: var(--w) var(--w) border-box no-repeat;background:conic-gradient(from 90deg at top var(--b) left var(--b),var(--_g)) 0 0 / var(--_p),conic-gradient(from 180deg at top var(--b) right var(--b),var(--_g)) 100% 0 / var(--_p),conic-gradient(from 0deg at bottom var(--b) left var(--b),var(--_g)) 0 100% / var(--_p),conic-gradient(from -90deg at bottom var(--b) right var(--b),var(--_g)) 100% 100% / var(--_p)}.mk-actions-list{display:flex;flex-direction:column;gap:8px}.mk-action{--icon-size: 14px;display:flex;gap:8px;font-size:14px;color:var(--mk-ui-text-tertiary)}.mk-editor-space-fragment{display:flex;flex-direction:column;height:100%;padding:var(--file-margins)}.mk-editor-actions-fields{display:flex;flex-direction:column;gap:8px}.mk-editor-actions-fields .mk-path-context-value{flex-wrap:nowrap}.mk-header-space{display:flex;flex-direction:column;gap:8px;width:100%;align-items:flex-start}.mk-editor-header{margin-bottom:12px}.mk-action{display:flex}.mk-action span{flex:1}.mk-tabs-space{display:flex;gap:8px;color:var(--mk-ui-text-tertiary);font-size:14px}.mk-tab-space{display:flex;gap:4px;padding:4px 8px;border-radius:8px}.mk-tab-space-active{color:var(--mk-ui-text-primary);border:thin solid var(--mk-ui-border);background:var(--mk-ui-background-contrast)}.mk-editor-code{border-radius:8px;border:thin solid var(--mk-ui-border);overflow:hidden}.mk-editor-tester{display:flex;padding:8px;min-height:24px;border-top:thin solid var(--mk-ui-border);align-items:center;width:100%;color:var(--mk-ui-text-tertiary);overflow:hidden;gap:4px}.mk-editor-tester span{flex:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mk-editor-actions-name{display:flex;gap:8px;width:100%;align-items:center;font-size:14px}.mk-editor-actions-name span{flex:1}.mk-editor-actions-nodes{display:flex;flex-direction:column;align-items:flex-start}.mk-editor-actions-body{display:flex;min-width:200px;flex-direction:column;border:thin solid var(--mk-ui-border);border-radius:8px;padding:8px;box-shadow:var(--shadow-s);background:var(--mk-ui-background-variant);gap:8px}.mk-formula{display:flex;flex-direction:column;width:500px}.mk-formula-header{padding:12px;background:var(--mk-ui-background);display:flex;gap:8px;align-items:flex-start}.mk-formula-header .cm-theme{flex:1}.mk-formula-list-section{font-size:14px;color:var(--mk-ui-text-tertiary);font-weight:var(--bold-weight)}.mk-formula-list-item{padding:4px 8px;border-radius:8px}.mk-formula-list-item:hover{background:var(--mk-ui-background-hover)}.mk-formula-helper{display:flex;height:280px}.mk-formula-list{display:flex;flex-direction:column;gap:8px;padding:12px 8px;max-height:100%;overflow-y:auto;width:140px;border-right:thin solid var(--mk-ui-divider)}.mk-formula-suggester{display:flex;flex:1;flex-direction:column;gap:8px;padding:16px;overflow-y:auto;max-height:100%}.mk-formula-suggester-args{display:flex;gap:8px;align-items:center}.mk-formula-suggester-arg{font-weight:400;color:var(--mk-ui-text-secondary)}.mk-formula-suggester-title{font-size:14px;color:var(--mk-ui-text-tertiary);font-weight:var(--bold-weight);margin-top:6px;border-top:thin solid var(--mk-ui-border);padding-top:8px}.mk-formula-suggestion{display:flex;gap:4px;border-radius:8px}.mk-formula-suggestion pre{padding:4px 12px;border-radius:8px;margin:0;background-color:var(--mk-ui-background);display:flex;flex:1}.mk-formula-suggestion-title span{flex:1}.mk-formula-suggester-name{--icon-size: 14px;font-size:20px;display:flex;align-items:center;font-weight:var(--bold-weight)}.mk-formula-suggester-name div{display:flex}.mk-formula-suggester-name svg{width:var(--icon-size);height:var(--icon-size)}.mk-editor-actions-node{display:flex;align-items:flex-start;flex-direction:column;gap:16px}.mk-editor-actions-children{display:flex;flex-direction:column;padding-left:6px;border-left:thin solid var(--mk-ui-divider);margin-left:6px;gap:8px;align-items:flex-start}.mk-editor-actions{display:flex;margin-top:8px;flex-direction:column;gap:8px;padding-bottom:100px}.mk-editor-context-selector{font-size:14px;display:flex;padding:8px;gap:8px;border-top:thin solid var(--mk-ui-divider);flex-direction:column}.mk-editor-context{overflow-x:scroll}.mk-editor-context-groups{display:flex;--icon-size: 14px;z-index:var(--layer-popover);background:var(--mk-ui-background);max-height:unset;-webkit-app-region:no-drag;padding:6px 10px;border:1px solid var(--mk-ui-border);background-color:var(--mk-ui-background-menu);user-select:none;border-radius:8px;align-items:center;height:50px;white-space:nowrap;gap:8px}.mk-editor-context-group-select{background:rgba(var(--mono-rgb-100),.025);border-radius:4px;overflow:hidden;display:flex;align-items:center;gap:2px;white-space:nowrap}.mk-editor-context-group .mk-path-context-field{width:auto;max-width:unset;min-width:auto;background:white;padding:2px 8px;gap:2px}.mk-editor-context-groups span{flex:1}.mk-editor-context-group{display:flex;gap:8px;align-items:center;padding:0 8px;border:thin solid var(--mk-ui-divider);border-radius:4px;height:32px;background:var(--mk-ui-background)}.mk-editor-context-properties{display:flex;flex-direction:column;padding:8px;background:var(--mk-ui-background-contrast);border-radius:4px}.mk-editor-context-properties>div{gap:4px;display:flex;flex-wrap:wrap}.mk-view-selector{margin-right:0;display:flex;align-items:center;gap:8px;overflow-x:auto;font-size:15px}body:not(.is-mobile) .mk-view-options{transition:all .2s ease}.mk-context-container{margin-bottom:12px}.mk-context-container:hover .mk-view-options{opacity:1}.mk-view-selector-spacer{min-width:24px;width:var(--file-margins)}.mk-view-selector>div>button:not(:last-child){padding-right:0}.mk-context-config{display:flex;gap:8px;align-items:center;margin-bottom:8px}.mk-context-config span{flex-grow:1}.mk-view-config-warning{display:flex;font-size:12px;background:var(--mk-ui-background-variant);padding:6px;border-radius:8px;gap:6px;overflow:hidden;align-items:center}.mk-view-config-warning>div{background:var(--mk-ui-background-active);padding:4px;border-radius:4px}.mk-view-config .mk-active button,.mk-view-config .mk-active{color:var(--mk-ui-text-primary)}button.mk-toolbar-button{padding:4px 8px!important;--icon-size: 14px;font-size:max(13px,1em);gap:8px;font-weight:var(--font-medium);background:rgba(var(--nav-item-background-active),.3);border:none;box-shadow:none;color:var(--mk-ui-text-tertiary);height:30px}button.mk-toolbar-button.mk-active{color:var(--mk-ui-text-primary);background:var(--nav-item-background-hover)}body:not(.is-mobile) button.mk-toolbar-button:hover{color:var(--mk-ui-text-primary);background:var(--mk-ui-background-hover)}.mk-view-config{width:100%;display:flex;gap:8px;align-items:center;margin-bottom:8px}.mk-view-search input{background:none;border:none;flex-grow:1}.mk-context-title-container{display:flex;align-items:center;gap:8px}body:not(.is-mobile) .mk-context-title .mk-toolbar-button{opacity:0}body:not(.is-mobile) .mk-context-title:hover .mk-toolbar-button{opacity:1}.mk-context-title{font-weight:var(--font-medium);flex-grow:10;white-space:nowrap;overflow:hidden;display:flex;align-items:center;gap:8px}.is-phone .mk-view-config{align-items:stretch;gap:0px;margin-top:8px}.is-phone .mk-toolbar-button{--icon-size: 16px;width:unset}.mk-view-search{background:var(--background-modifier-form-field);border:var(--input-border-width) solid var(--background-modifier-border);display:flex;border-radius:8px;align-items:center;flex-grow:1}.mk-view-search button:hover{background:unset!important}.mk-view-search button{margin:0!important;padding:4px 8px!important}.is-phone .mk-view-options{margin-top:8px;margin-bottom:8px}.mk-view-options{display:flex;align-items:center;color:var(--mk-ui-text-primary)!important;gap:4px;flex:1}.mk-view-options span{flex:1}.mk-view-config span{flex-grow:10}.mk-toolbar-button svg{width:var(--icon-size);height:var(--icon-size)}.mk-filter-bar{display:flex;padding:4px;gap:8px;width:100%;flex-wrap:wrap}.mk-filter-bar span{flex-grow:1}.mk-view-config .mk-active{color:var(--mk-ui-text-primary)!important}.mk-filter{border-radius:6px;overflow:initial;display:flex;font-size:13px;white-space:nowrap;padding:0;background:var(--background-modifier-form-field);border:var(--input-border-width) solid var(--background-modifier-border);color:var(--mk-ui-text-primary)}.mk-query .mk-filter>span{background:var(--mk-ui-background)}.mk-filter>span>span{display:flex;background:var(--mk-ui-background);padding:0 4px;border-radius:4px}.mk-filter>span,.mk-filter>div{background:var(--mk-ui-background-variant)}.mk-filter>span:hover,.mk-filter>div:hover{background:var(--mk-ui-active-hover)}.mk-filter>*:first-child{border-top-left-radius:4px;border-bottom-left-radius:4px;padding-left:10px}.mk-filter>*:last-child{border-top-right-radius:4px;border-bottom-right-radius:4px;padding:6px;border-right:none}.mk-filter>span{border-right:thin solid var(--mk-ui-divider);padding:4px 8px;display:flex;align-items:center;gap:4px}.mk-filter input{height:100%;width:100px!important;border:none}.mk-filter .mk-path{padding:0 4px}.mk-filter div{display:flex;align-items:center}.mk-filter div svg{width:14px;height:14px}.mk-query{width:100%;display:flex;flex-direction:column;gap:16px}.mk-query-filters{flex-wrap:wrap;border-bottom:none;display:flex;background:var(--mk-ui-background);padding:8px;border-radius:8px}.mk-query-filter,.mk-query-group-type{display:flex}.mk-query-group{display:flex;gap:8px}.mk-query-group-type{width:32px;padding-bottom:4px}.mk-query-group button{background-color:unset;box-shadow:none;width:unset}.is-phone .mk-query-group button{background-color:var(--mk-ui-active-normal)}.mk-query-group-type>span,.mk-query-filter>span{display:flex;flex-grow:1}button.mk-filter-add{background-color:transparent}.mk-filter-add{border-radius:4px;box-shadow:none!important;background:none;display:flex;align-items:center;padding:4px 8px;font-size:13px;color:var(--mk-ui-text-tertiary);gap:4px}.mk-filter-add:hover{background:var(--mk-ui-background-hover);color:var(--mk-ui-text-secondary)}.mk-filter-add span{display:flex}.mk-tag-selector{display:flex;gap:4px;margin-top:8px;margin-bottom:8px;flex-wrap:wrap}.mk-tag-selector svg{color:var(--tag-color)!important}.mk-tag-selector div{cursor:var(--cursor-link);font-size:12px;display:flex}.mk-tag-selector span{display:flex;align-items:center}.mk-tag-selector button{height:auto;border-radius:var(--tag-radius);color:var(--tag-color);background-color:var(--tag-background);border:var(--tag-border-width) solid var(--tag-border-color);box-shadow:none!important;font-size:var(--tag-size);vertical-align:baseline;border-left:none;border-right:none;padding-top:var(--tag-padding-y)!important;padding-bottom:var(--tag-padding-y)!important}.mk-floweditor .workspace-leaf{all:unset}.mk-floweditor>.workspace-leaf>.workspace-leaf-content>.view-header{display:none}.mk-floweditor>.workspace-leaf>.workspace-leaf-content{border:0!important}.mk-flow-minimal .mk-flowspace-editor:not(.mk-flow-node)>.mk-floweditor>.workspace-leaf>.workspace-leaf-content>.view-content>.mod-cm6{padding:8px 8px 8px 18px;border:thin solid var(--mk-ui-active-hover);margin:8px 0;background:rgba(var(--mono-rgb-100),.025);border-radius:8px}.mk-floweditor.hover-editor .popover-content{margin:0;overflow:hidden}.mk-floweditor .markdown-preview-view{padding:0}.mk-floweditor.hover-editor .popover-content .workspace-split{display:none}.mk-floweditor.hover-editor .workspace-leaf,.mk-floweditor.hover-editor .workspace-split{height:100%;width:100%}.mk-floweditor .markdown-source-view.mod-cm6 .cm-editor{min-height:auto}.mk-floweditor .embedded-backlinks,.mk-floweditor .metadata-container{display:none!important}.mk-floweditor .workspace-leaf-content[data-type=canvas] .view-content{min-height:600px;height:calc(100vh - 100px)}.mk-floweditor-container .mk-floweditor .cm-content,div:not(.mk-flowspace-editor)>.mk-floweditor .cm-content{padding:0!important}.markdown-source-view.mod-cm6 .cm-content>.internal-embed.markdown-embed{contain:unset!important}.mk-toggle-on{color:var(--mk-ui-active)}.mk-floweditor .view-content{background:none!important}.mk-floweditor .view-content .pdf-container{min-height:600px}.mk-floweditor-sticker{display:inline-block;--icon-container-size: 20px;--icon-size: 13px}.mk-floweditor-toggle{display:inline-block;--icon-container-size: 20px;--icon-size: 16px}.mk-floweditor .mk-floweditor-title-container{display:flex}.mk-floweditor-title{padding:8px 0;margin:8px 0 0;border-top:1px solid var(--mk-ui-border);width:100%;display:flex}.mk-floweditor-title:hover{background:var(--mk-ui-background-hover)}.mk-floweditor-title div:not(.collapse) svg{transform:rotate(0)}.mk-floweditor-title .collapse svg{transform:rotate(90deg)}.mk-floweditor-title svg{margin-left:4px;width:10px;height:10px}.mk-flow-hover{margin-top:-34px;margin-left:-34px}.mk-flow-hover>div{display:flex;gap:8px;align-items:center}.mk-flow-minimal .mk-floweditor-container:not(.mk-floweditor-fix)>.mk-floweditor{padding:8px;border-radius:4px;border-left:thin solid var(--mk-ui-divider)}.mk-flow-minimal .markdown-embed-title,.mk-flow-seamless .markdown-embed-title{display:none}.mk-foldernote>.mk-floweditor>.popover-content>.mk-flow-titlebar,.mk-flow-minimal .mk-flow-titlebar,.mk-flow-seamless .mk-flow-titlebar{display:none}.mk-flow-minimal .internal-embed>.markdown-embed,.mk-flow-minimal .internal-embed.markdown-embed{padding:8px;border-radius:4px;border:thin solid var(--mk-ui-divider);margin-top:4px}.mk-flow-seamless .internal-embed>.markdown-embed,.mk-flow-seamless .internal-embed.markdown-embed{margin-top:4px}.internal-embed>.markdown-embed .markdown-rendered h1,.internal-embed>.markdown-embed .markdown-rendered h2,.internal-embed>.markdown-embed .markdown-rendered h3,.internal-embed>.markdown-embed .markdown-rendered h4,.internal-embed>.markdown-embed .markdown-rendered h5,.internal-embed>.markdown-embed .markdown-rendered h6,.internal-embed.markdown-embed .markdown-rendered h1,.internal-embed.markdown-embed .markdown-rendered h2,.internal-embed.markdown-embed .markdown-rendered h3,.internal-embed.markdown-embed .markdown-rendered h4,.internal-embed.markdown-embed .markdown-rendered h5,.internal-embed.markdown-embed .markdown-rendered h6{margin:0}.markdown-embed p{margin-bottom:24px}.mk-flow-minimal .internal-embed>.markdown-embed,.mk-flow-minimal .internal-embed>.markdown-embed,.mk-flow-seamless .internal-embed.markdown-embed,.mk-flow-seamless .internal-embed.markdown-embed{margin-top:24px}.mk-floweditor-container{min-height:var(--flow-height)}.mk-floweditor>.workspace-leaf>.workspace-leaf-content,.mk-floweditor>.workspace-leaf>.workspace-leaf-content>.view-content,.mk-floweditor>.workspace-leaf>.workspace-leaf-content>.view-content>.mod-cm6>.cm-editor>.cm-scroller{overflow:visible!important;height:auto}.mk-floweditor-container>.mk-floweditor{border:thin solid transparent}.mk-floweditor-container{display:inline-block;width:100%}.cm-tooltip-hover{margin-bottom:30px}.mk-floweditor-fix>.mk-floweditor{margin-top:-28px}.cm-tooltip{border:none!important;z-index:var(--layer-popover)!important}.cm-line:hover>.mk-floweditor-selector,.mk-floweditor-selector:hover{visibility:visible}.internal-embed.markdown-embed:hover .mk-floweditor-selector,.internal-embed>.markdown-embed:hover .mk-floweditor-selector{visibility:visible}.mk-floweditor-selector{--icon-size: 14px;position:absolute;right:0;top:-34px;z-index:var(--layer-popover);display:flex;visibility:hidden}.mk-flowblock-menu{padding:4px;margin-bottom:4px;align-items:center;display:flex;font-size:14px;background:var(--mk-ui-background);gap:8px;border:thin solid var(--mk-ui-border);border-radius:4px;box-shadow:var(--mk-shadow-card)}.mk-cell-image .mk-hover-button:hover{color:var(--mk-ui-text-primary);background:var(--mk-ui-background-contrast)}button.mk-hover-button{--icon-size: 12px;font-size:12px;padding:4px 8px;display:flex;gap:4px;border:thin solid var(--mk-ui-border-overlay);border-radius:4px;background:var(--mk-ui-background-overlay)}.mk-cell-image .mk-hover-button{color:var(--mk-ui-text-secondary)}.mk-flowblock-menu .mk-toolbar-button{height:24px}.mk-hover-button svg{width:var(--icon-size);height:var(--icon-size)}.mk-flowblock-menu .mk-hover-button:last-child{border-right:none}.mk-flowblock-menu .mk-hover-button:hover{background:var(--nav-item-background-hover);border-radius:4px}.mk-floweditor>.workspace-leaf>.workspace-leaf-content>.view-content>.markdown-source-view.mod-cm6>.cm-editor>.cm-scroller{padding:0}.mk-floweditor>.workspace-leaf>.workspace-leaf-content>.view-content>div>.mk-table{padding-bottom:10px!important}.is-phone.mk-flow-state .workspace-split{padding-top:0}.mk-flow-state .workspace-tab-header-container-inner,.mk-flow-state .workspace-ribbon{display:none}body:not(.is-mobile).mk-flow-state .workspace-split.mod-left-split .workspace-sidedock-vault-profile{transform:translateY(-100%);transition:transform .3s linear}body.mk-flow-state{--tab-container-background: var(--background-primary) !important;--titlebar-background-focused: var(--background-primary) !important}.mk-flow-state .workspace-tabs .workspace-leaf{background:var(--background-primary)!important}.mk-flow-state .view-header{transform:translateY(-100%);max-height:0;transition:transform .3s linear}.mk-props-contexts{display:flex;flex-direction:column;gap:8px;position:relative;margin-bottom:12px;width:100%;align-items:flex-start}.mk-props-value{display:flex;flex-direction:column;gap:8px;align-items:flex-start}.mk-props-list{display:flex;flex-wrap:wrap;gap:8px;width:100%;align-items:center}.mk-props-list .mk-path{width:100px}.mk-props-contexts .mk-path-context-row{width:100%}.mk-props-contexts .mk-divider{border-bottom:thin solid var(--mk-ui-divider);width:100%;height:1px}.mk-props-contexts-space{display:flex;flex-direction:column;align-items:flex-start;gap:4px}.mk-props-contexts .mk-cell-boolean{padding:0}.mk-props-contexts-space-list{display:flex;flex-wrap:wrap;gap:4px}.mk-props-contexts-space-name{display:flex;background:var(--tag-background);color:var(--tag-color);align-items:center;padding:2px 8px;font-size:12px;gap:4px;border-radius:12px;border:var(--tag-border-width) solid var(--tag-border-color)}.mk-props-contexts-space-add{display:flex;align-items:center;padding:4px;border-radius:4px}.mk-props-contexts-space-add:hover{background:var(--mk-ui-background-hover)}.mk-props-contexts-space-name .mk-path-icon{--icon-container-size: 16px;--icon-size: 16px}.mk-props-contexts-space-props{margin-left:8px}.mk-props-pill{display:flex;align-items:center;gap:4px;padding:4px 8px;border-radius:12px;background:var(--mk-ui-background-contrast);color:var(--mk-ui-text-tertiary);font-size:12px}.mk-props-contexts-space-name:hover{opacity:1!important}.mk-fold{position:absolute;left:-24px;top:6px}.mk-frame-insert{width:100%;bottom:0;display:flex;position:fixed;height:30px;border-radius:4px;margin-top:4px;margin-left:4px;--max-width: var(--file-line-width);max-width:min(100%,var(--max-width))!important;margin-left:calc((max(100%,var(--max-width)) - var(--max-width)) /2)!important}.mk-frame-insert:hover{background:var(--mk-ui-background-hover)!important}.mk-frame-slides-editor{display:flex;padding:4px;font-size:12px;z-index:var(--layer-popover);background:var(--mk-ui-background-menu);max-height:unset;border-bottom:1px solid var(--mk-ui-border)}.mk-node-flow{display:flex;flex-direction:column;--icon-container-size: 16px;--icon-size: 14px;align-items:flex-start;height:100%}.mk-path-view{display:flex;flex-direction:column;overflow-y:clip;animation:.15s ease-out 0s 1 fadeInFromNone;width:100%;height:100%}.mk-node-input{padding:0!important}.mk-node-text-placeholder{color:var(--mk-ui-text-tertiary)}.mk-path-view iframe{width:100%;height:100%}.mk-editor-frame-node-container{display:flex;position:absolute;flex-direction:column;bottom:calc(100% + 10px);padding:0;z-index:var(--layer-popover);gap:10px;align-items:flex-start}.mk-editor-frame-node-selector{--icon-size: 14px;display:flex;z-index:var(--layer-popover);background:var(--mk-ui-background);max-height:unset;-webkit-app-region:no-drag;padding:6px;border:1px solid var(--mk-ui-border);background-color:var(--mk-ui-background-menu);box-shadow:var(--shadow-s);user-select:none;border-radius:8px;align-items:center;height:50px;white-space:nowrap}.mk-editor-frame-node-selector{font-size:12px;width:100%;overflow-x:auto;overflow-y:none}.mk-editor-frame-node-menu{-webkit-app-region:no-drag;padding:6px;user-select:none;display:flex}.mk-editor-frame-properties{display:flex;max-height:inherit;overflow:scroll;gap:8px;flex-direction:column}.mk-editor-frame-properties>div{padding:8px}.mk-editor-frame-properties>.mk-editor-actions-name{border-bottom:thin solid var(--mk-ui-border);padding:8px}.mk-editor-frame-property{display:flex;gap:8px;align-items:center;position:relative;height:100%;background:var(--mk-ui-background);padding:4px 8px}.mk-editor-frame-node-selector svg{--icon-color: var(--mk-ui-text-secondary);width:var(--icon-size);height:var(--icon-size);color:var(--icon-color)}.mk-editor-frame-node-selector .mk-setter-color{border-radius:50%}.is-mobile .mk-editor-frame-node-selector{margin-top:0;height:48px;border-top:1px solid var(--mk-ui-divider)}.mk-editor-frame-node-button{margin:4px;padding:4px;border-radius:4px;display:flex;font-size:13px;align-items:center;gap:4px;white-space:nowrap;z-index:var(--layer-tooltip)}.mk-editor-frame-node-button svg{width:var(--icon-size);height:var(--icon-size)}.mk-editor-frame-properties .mk-active{color:var(--mk-color-ui-accent)}.mk-editor-frame-properties .mk-active svg{--icon-color: var(--mk-color-ui-accent)}.mk-editor-frame-node-selector input{width:100%;border:0;background:rgba(var(--mono-rgb-100),.025)}.mk-editor-frame-node-selector .mk-active p{min-width:0;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;margin:0}.mk-editor-frame-node-selector .mk-mark-option>div:first-child{flex:1;min-width:80px;max-width:100%;max-height:100%}.mk-editor-frame-node-button-primary{-webkit-app-region:no-drag;display:inline-flex;align-items:center;justify-content:center;color:var(--text-normal);font-size:var(--font-ui-small);border-radius:var(--button-radius);border:0;padding:var(--size-4-1) var(--size-4-3);height:var(--input-height);font-weight:var(--input-font-weight);cursor:var(--cursor);background-color:var(--interactive-normal);box-shadow:var(--input-shadow)}.mk-editor-frame-node-button>div{display:flex}.mk-editor-frame-node-button:hover,.mk-editor-frame-node-button.mk-active,.mk-editor-frame-node-selector .mk-mark-active{background:var(--mk-ui-background-menu-hover)}.mk-editor-frame-node-selector .mk-divider{border-left:thin solid var(--mk-ui-divider);width:1px;height:100%}.mk-editor-frame-property .mk-cell-text{width:100px;overflow:hidden}.mk-setter-step{display:flex;align-items:center;gap:4px;padding:0 4px}.mk-setter-step input{width:50px;padding-right:0;border:0}.mk-setter-step input[type=number]::-webkit-outer-spin-button,.mk-setter-step input[type=number]::-webkit-inner-spin-button{-webkit-appearance:inner-spin-button!important}.mk-setter-text span,.mk-setter-step span{font-size:12px;padding:4px}.mk-editor-alignment-menu{display:flex;width:46px;height:46px;padding:4px;flex-wrap:wrap;gap:4px}.mk-editor-alignment-selector{display:flex;width:10px;height:10px;border:thin solid var(--mk-ui-border)}.mk-editor-alignment-selector.mk-active{background:var(--mk-ui-border)}.mk-editor-frame-property-name{display:flex;white-space:nowrap;font-size:14px}.mk-mark-prop{display:flex;position:absolute;top:0;right:-4px;align-items:center;justify-content:center;width:8px;height:8px}.mk-mark-prop>div{display:flex;align-items:center;width:5px;height:5px;cursor:pointer}.mk-frame-corner{width:8px;height:8px;margin-left:-4px;margin-top:-4px;border:1px solid var(--mk-ui-border-accent);border-radius:4px;background:var(--mk-ui-handle-fill);z-index:200;visibility:visible}.mk-f-edit:hover .mk-frame-padding,.mk-f-edit:hover .mk-frame-gap,.mk-f-edit:hover .mk-frame-corner,.mk-f-edit-hover .mk-frame-padding,.mk-f-edit-hover .mk-frame-gap,.mk-f-edit-hover .mk-frame-corner{visibility:visible}.mk-frame-padding-handle-h:hover,.mk-frame-padding-handle-v:hover,.mk-frame-paddings.mk-modifier-shift:has(.mk-frame-padding-handle-v:hover) .mk-frame-padding-handle-v,.mk-frame-paddings.mk-modifier-shift:has(.mk-frame-padding-handle-v:hover) .mk-frame-padding-handle-h,.mk-frame-paddings.mk-modifier-shift:has(.mk-frame-padding-handle-h:hover) .mk-frame-padding-handle-h,.mk-frame-paddings.mk-modifier-shift:has(.mk-frame-padding-handle-h:hover) .mk-frame-padding-handle-v{background:repeating-linear-gradient(-45deg,transparent,transparent 4px,var(--mk-ui-handle-color-hover) 4px,var(--mk-ui-handle-color-hover) 5px)}.mk-frame-resize-handle:before{content:"";display:block;position:absolute;width:8px;height:8px;border:1px solid var(--mk-ui-border-accent);background:var(--mk-ui-handle-fill);margin:6px}.mk-frame-padding-handle-v,.mk-frame-padding-handle-h{width:100%;height:100%;visibility:visible;display:flex}.mk-frame-padding-handle-v>.mk-frame-draggable-handle{height:20px!important;width:8px!important;margin:auto;display:flex}.mk-frame-padding-handle-v>.mk-frame-draggable-handle>span{background:var(--mk-ui-handle-color);height:10px!important;width:2px!important;margin:auto}.mk-frame-padding-handle-h>.mk-frame-draggable-handle{width:20px!important;height:8px!important;margin:auto;display:flex}.mk-frame-padding-handle-h>.mk-frame-draggable-handle>span{background:var(--mk-ui-handle-color);height:2px;width:10px;margin:auto}.mk-frame-gap-handle-h{height:10px;visibility:visible;display:flex}.mk-frame-gap-handle-v{width:10px;visibility:visible;display:flex}.mk-frame-gap-handle-h>div{width:10px!important;height:2px!important;background:var(--mk-ui-handle-color);outline:1px solid var(--mk-ui-handle-outline);margin:auto}.mk-frame-padding-handle-h:hover .mk-frame-draggable-handle:before,.mk-frame-padding-handle-v:hover .mk-frame-draggable-handle:before,.mk-frame-gap-handle-h:hover .mk-frame-draggable-handle:before,.mk-frame-gap-handle-v:hover .mk-frame-draggable-handle:before{content:attr(data-placeholder);position:absolute;background:var(--mk-ui-border-accent);font-size:12px;border-radius:2px;transform:translate(10px,10px);padding:2px;--icon-size: 11px}.mk-frame-gap-handle-v>div{height:10px!important;width:2px!important;background:var(--mk-ui-handle-color);outline:1px solid var(--mk-ui-handle-outline);margin:auto}.mk-frame-gaps:has(.mk-frame-gap-handle-v:hover) .mk-frame-gap-handle-v,.mk-frame-gaps:has(.mk-frame-gap-handle-v:hover) .mk-frame-gap-handle-h,.mk-frame-gaps:has(.mk-frame-gap-handle-h:hover) .mk-frame-gap-handle-v,.mk-frame-gaps:has(.mk-frame-gap-handle-h:hover) .mk-frame-gap-handle-h{background:repeating-linear-gradient(-45deg,transparent,transparent 3px,var(--mk-ui-handle-color-hover) 3px,var(--mk-ui-handle-color-hover) 5px)}.mk-frame-bounds{position:absolute!important;width:100%;height:100%;z-index:var(--mk-layer-editor-overlay);outline:thin solid var(--mk-ui-border-accent)}.mk-frame-bounds.mk-selected{border:2px solid var(--color-accent)}.mk-frame-resize-label-width svg,.mk-frame-resize-label-height svg{width:var(--icon-size);height:var(--icon-size)}.mk-frame-resize-label-width{--icon-size: 14px;color:var(--mk-ui-text-accent);position:absolute;bottom:-26px;left:50%;z-index:var(--mk-layer-editor-overlay);pointer-events:auto}.mk-frame-resize-label-width>div{background:var(--mk-ui-border-accent);font-size:12px;border-radius:2px;margin-left:-50%;display:flex;width:100%;padding:2px;gap:2px;--icon-size: 14px;align-items:center}.mk-frame-resize-label-height{--icon-size: 14px;color:var(--mk-ui-text-accent);position:absolute;right:-42px;z-index:var(--mk-layer-editor-overlay);top:50%;pointer-events:auto}.mk-frame-resize-label-height>div{background:var(--mk-ui-border-accent);font-size:12px;border-radius:2px;margin-top:-50%;display:flex;width:100%;padding:2px;gap:2px;--icon-size: 14px;align-items:center}.mk-frame-fill{background:repeating-linear-gradient(-36deg,transparent,transparent 9px,var(--mk-ui-border-accent) 9px,var(--mk-ui-border-accent) 10px)}.mk-frame-column{position:absolute!important;width:100%;height:100%;z-index:var(--layer-popover)}.mk-frame-column-resize-handle:hover{border-right:1px solid var(--nav-indentation-guide-color)}.mk-frame-column-placeholder{position:absolute;right:-16px;width:16px;bottom:0}.is-mobile .mk-editor-frame-hover-menu-container{right:-14px}.mk-editor-frame-hover-menu-container{margin-left:-30px;position:absolute;padding:4px;z-index:var(--layer-popover);pointer-events:auto}.mk-editor-frame-hover-menu-container .mk-editor-frame-hover-button svg{width:20px;height:20px}.mk-editor-frame-hover-menu-container .mk-editor-frame-hover-button:hover{background:var(--nav-item-background-hover);border-radius:4px}.mk-editor-frame-hover-menu,.mk-editor-frame-hover-horizontal{transition:all .2s ease;border-radius:4px;display:flex;font-size:12px;background:var(--mk-ui-background);gap:4px;flex-direction:column}.mk-frame-drop-zone-container{position:absolute;top:0;left:0;z-index:var(--layer-popover)}.mk-frame-drop-zone{position:absolute}.mk-indicator-bottom:before{position:absolute;content:" ";display:block;width:100%;height:2px;bottom:0;border-radius:1px;background:var(--mk-ui-active)}.mk-indicator-right:before{position:absolute;content:" ";display:block;width:2px;height:100%;left:0;border-radius:1px;background:var(--mk-ui-active)}.mk-indicator-insert:before{position:absolute;content:" ";display:block;width:100%;height:2px;bottom:0;border-radius:1px;background:yellow}.mk-indicator-top:before{position:absolute;content:" ";display:block;width:100%;height:2px;top:0;border-radius:1px;background:var(--mk-ui-active)}.mk-indicator-left:before{position:absolute;content:" ";display:block;width:2px;height:100%;right:0;border-radius:1px;background:var(--mk-ui-active)}.mk-frame-slides{display:flex}.mk-frame-slide,.mk-frame-slide-active{display:flex;flex-direction:column}.mk-frame-slide span:first-child,.mk-frame-slide-active span:first-child{border-left:thin solid var(--mk-ui-divider);font-size:8px}.mk-frame-slide span:last-child{width:20px;height:20px;background:var(--background-primary-alt)}.mk-frame-slide-active span:last-child{width:20px;height:20px;background:var(--background-secondary)}.mk-date-picker-container{display:flex;gap:8px;align-items:center;padding:12px;width:280px;flex-direction:column}.mk-date-picker{display:flex;gap:8px;align-items:center;width:100%}.mk-date-picker table{border-spacing:0;table-layout:fixed;width:100%}.mk-date-picker-header{display:flex;width:100%;justify-content:space-between;align-items:center;font-size:14px;--icon-size: 16px;--icon-container-size: 24px}.mk-date-picker-header .mk-date-picker-header-input{display:flex;gap:4px}.mk-date-picker-header-input input:first-child{width:30px}.mk-date-picker-header-input input{width:60px;text-align:right;padding:4px;border-radius:4px;border:thin solid var(--mk-ui-border)}.mk-date-picker-time{display:flex;gap:8px;align-items:center;--icon-size: 16px;--icon-container-size: 30px}.mk-date-picker-time input{width:40px;text-align:right;padding:4px;border-radius:4px;border:thin solid var(--mk-ui-border)}.mk-date-picker-time svg{width:var(--icon-size);height:var(--icon-size)}.mk-date-picker-header button,.mk-date-picker-time button{background:none;box-shadow:none;border:thin solid var(--mk-ui-border);width:var(--icon-container-size);height:var(--icon-container-size);padding:0}button.mk-date-picker-day.mk-date-picker-today{background:var(--mk-ui-background-active)}button.mk-date-picker-day.mk-date-picker-selected{background:var(--mk-ui-background-reverse);color:var(--mk-ui-text-reverse)}.mk-date-picker-header button svg{width:var(--icon-size);height:var(--icon-size)}button.mk-date-picker-day{width:100%;background:none;box-shadow:none;padding:0;display:flex;align-items:center;justify-content:center;font-size:14px;border-radius:4px}button.mk-date-picker-day:not(.mk-date-picker-selected):hover{background:var(--mk-ui-background-hover)}.mk-date-picker-cell{width:14%}.mk-date-picker-day:hover{background:var(--mk-ui-background-hover);border-radius:4px}.mk-date-picker-months{width:100%}.mk-date-picker-month{width:100%;display:flex;flex-direction:column;gap:8px}.mk-ui-color-picker{display:flex;flex-direction:column;gap:4px}.mk-ui-color-picker-selector{border-bottom:thin solid var(--mk-ui-divider)}.mk-ui-color-picker-palette{padding:12px;display:flex;flex-direction:column}.mk-ui-color-picker .mk-color{margin:4px}.mk-ui-color-picker-palette>div{display:flex;flex-direction:row;gap:4px}body:not(.is-mobile) .mk-style-menu{margin-left:-80px}.mk-style-menu .mk-divider{border-left:thin solid var(--mk-ui-divider);width:1px;height:24px}.mk-style-menu{display:flex;padding:4px;margin-top:-50px;align-items:center;gap:4px;border:1px solid var(--background-modifier-border-hover);background-color:var(--background-secondary);border-radius:var(--radius-m);box-shadow:var(--shadow-s);z-index:var(--layer-menu)}.mk-style-toolbar svg,.mk-style-menu svg{width:16px;height:16px}.mk-style-toolbar .mk-mark,.mk-style-menu .mk-mark{width:28px;height:28px;padding:6px}.mk-style-toolbar{--mobile-toolbar-height: 48px;border-radius:0;width:100%;margin-top:0;overflow-x:auto;justify-content:center;height:100%;display:flex;gap:12px;align-items:center}.mk-style-menu .mk-mark{margin:4px;border-radius:4px;display:flex}.mk-mobile-styler .mobile-toolbar-options-list,.mk-mobile-styler .mobile-toolbar-floating-options{display:none}.mk-mark div,.mk-mark-group{display:flex}.mk-mark-dropdown{padding:2px;border-radius:4px;display:flex;align-items:center}.mk-mark-dropdown svg{width:12px;height:12px;transform:rotate(90deg)}.mk-style-menu .mk-mark:hover,.mk-style-menu .mk-mark-dropdown:hover,.mk-style-menu .mk-mark-active{background:var(--mk-ui-background-hover)}.mk-style-menu svg{color:var(--mk-ui-text-secondary)}.mk-color{width:24px;height:24px;border-radius:12px;margin:8px;border:thin solid var(--mk-ui-border)}.mk-color:hover{opacity:.8}mark{color:unset;border-radius:2px;margin:0 2px;padding:0 2px}.mk-main-menu-container{display:flex;flex-direction:column;margin:12px 10px 0 12px;gap:4px;align-items:center}.mk-main-menu-inner{display:flex;width:100%;align-items:center;gap:8px}.mk-main-menu{display:flex;transition:all .2s ease;align-items:center;gap:8px;flex:1;overflow:hidden}.mk-main-menu.mk-hidden{transform:translate(-100%);opacity:0;pointer-events:none}.mk-main-menu-search{transition:all .2s ease;align-items:center;gap:8px}.mk-main-menu-search.mk-hidden{opacity:0;pointer-events:none}.mk-main-menu-button{font-weight:var(--font-medium);font-size:14px;padding:8px 10px;text-align:left;border-radius:4px;align-items:center;display:flex;gap:4px;justify-content:center}.mk-main-menu-search{position:absolute;display:flex;width:calc(100% - 60px)}.mk-main-menu-search input{width:100%}.mk-main-menu-icon{background:var(--nav-item-background-hover);padding:2px;border-radius:2px;text-transform:uppercase;width:20px;height:20px;font-size:12px;justify-content:center;margin-right:4px}.is-mobile .mk-main-menu-button{font-size:16px;font-weight:var(--font-medium);gap:4px;padding:8px 12px!important}.mk-main-menu-button>div{display:flex}.workspace-drawer.mod-left.is-pinned{min-width:350px}.mk-main-menu-button.mk-main-menu-button-primary{flex-grow:1;line-height:1;justify-content:flex-start;overflow:hidden}.mk-main-menu-button-primary span{text-overflow:ellipsis;overflow:hidden;display:inline-block;white-space:nowrap}.mk-main-menu .mk-main-menu-sticker{margin-right:8px}.mk-main-menu-button svg{height:16px;width:16px}body:not(.is-mobile) .mk-main-menu-button:hover{background:var(--nav-item-background-hover)}.mk-menu-button{display:flex;padding:.5rem;font-size:.75rem;line-height:1.25rem;align-items:center;width:100%;border-radius:.375rem}.mk-menu-button:hover{background:var(--nav-item-background-hover)}.mk-main-menu-container .mk-query{width:100%}.mk-slash-item{display:flex;align-items:center}.mk-slash-icon{display:flex;margin-right:8px}.mk-slash-icon svg{width:16px;height:16px}.cm-focused .cm-active.mk-placeholder:before{content:attr(data-ph);color:var(--mk-ui-text-tertiary);position:absolute}.mk-floweditor .cm-active.mk-placeholder:before{content:attr(data-ph);color:var(--mk-ui-text-tertiary);position:absolute}@keyframes slideInFromLeft{0%{transform-origin:top 50%;transform:scale(.5);opacity:.5}to{transform:scale(1);opacity:1}}@keyframes fadeInFromNone{0%{display:none;opacity:0}1%{display:block;opacity:0}to{display:block;opacity:1}}.mk-menu{--mk-menu-max-height: calc(100vh - var(--header-height));padding:0;-webkit-app-region:no-drag;border:1px solid var(--mk-ui-border);background-color:var(--mk-ui-background-menu);border-radius:var(--mk-ui-radius-medium);box-shadow:var(--shadow-s);position:fixed;z-index:var(--layer-menu);user-select:none;max-height:calc(100vh - var(--header-height));overflow:hidden}.mk-suggester{--mk-menu-max-height: 200px !important;min-width:200px}.mk-suggester[data-placeholder]:empty:before{content:attr(data-placeholder);color:var(--mk-ui-text-tertiary)}.mk-menu-wrapper{max-height:inherit}.mk-menu-suggester{visibility:hidden}.mk-combo-menu{height:90%}.mk-drawer-overlay{position:fixed;inset:0;z-index:calc(var(--layer-menu) + calc(var(--drawer-index) * 2));background-color:var(--mk-ui-background-overlay)}.mk-drawer-content.mk-drawer-palette{top:var(--safe-area-inset-top)!important;bottom:0;height:auto!important;max-height:auto!important}.mk-drawer-handle{margin-bottom:12px;background:var(--mk-ui-divider);min-height:5px}.app-container[vaul-drawer-wrapper]{transform-origin:center top}.workspace-split{padding-top:var(--safe-area-inset-top);background:var(--editor-bg-color)}body.is-mobile{padding-top:0!important}.mk-drawer-content.mk-drawer-modal{width:90%;margin:0 auto 50px;padding:14px;border-radius:8px}.mk-drawer-content.mk-drawer-modal .mk-drawer-handle{display:none}[vaul-drawer][vaul-drawer-direction=bottom]:after{display:none}.mk-drawer-content{position:absolute;box-shadow:var(--mk-shadow-menu);background:var(--mk-ui-background);z-index:calc(var(--layer-menu) + calc(var(--drawer-index) * 2) + 1);max-height:96%;width:100%;margin-top:24px;left:0;right:0;padding-bottom:var(--safe-area-inset-bottom);bottom:0;overflow:auto;border-top-left-radius:8px;border-top-right-radius:8px;padding-top:12px;display:flex;flex-direction:column}.mk-menu-suggester.mk-ready{animation:.15s ease-out 0s 1 slideInFromBottom;visibility:visible}.mk-drawer-content .mk-menu-container{width:100%;padding-bottom:var(--safe-area-inset-bottom)}.mk-menu svg{--icon-size: 16px;--icon-stroke: 2px}.mk-menu-input.selected{background-color:unset!important}.mk-menu-input input{padding:4px;outline:0;border-radius:var(--mk-ui-radius-small);min-width:0;width:100%}.mk-menu-separator{height:0;margin:6px -6px;border-bottom:1px solid var(--mk-ui-border)}.mk-menu-separator:last-child,.mk-menu-separator:first-child{display:none}.mk-menu-separator+.mk-menu-separator{display:none}.mk-menu-input-wrapper{width:100%}.is-phone .mk-menu-input-wrapper{padding:8px}.is-phone .mk-menu-search{background:var(--mk-ui-background-input);border-radius:8px;margin-top:0;border-bottom:none}.mk-menu-container{height:100%;position:relative;width:240px;flex-direction:column;font-size:1em;line-height:1.2;display:flex;flex-wrap:wrap;cursor:text}.mk-menu-sections{display:flex;padding:6px 8px;width:100%;gap:8px}.mk-menu-section{background:var(--mk-ui-background);padding:4px 6px;border-radius:4px;white-space:nowrap;font-size:12px}.mk-menu-section.is-active{background:var(--mk-ui-active);color:var(--text-on-accent)}.mk-menu-selected-tag-wrapper{padding:8px;display:flex;gap:8px;flex-wrap:wrap}.mk-menu-selected-tag{display:inline-block;box-sizing:border-box;padding:4px 8px;border-radius:4px;box-shadow:unset!important;font-size:12px;line-height:inherit}.mk-menu-selected-tag:after{content:"\2715";color:#aaa;margin-left:8px}.mk-menu-selected-tag:hover,.mk-menu-selected-tag:focus{border-color:#b1b1b1}.mk-menu-search{width:100%;display:flex;padding:0 10px;margin-top:4px;border-bottom:1px solid var(--mk-ui-border)}button.mk-menu-search-button{background:none;border:none;box-shadow:none;--icon-size: 16px}button.mk-menu-search-button svg{width:var(--icon-size);height:var(--icon-size)}.mk-menu-search-container{display:flex;gap:8px;padding:7px 2px;max-width:100%;width:100%}.mk-menu-search-input{max-width:100%;background:unset!important;margin:0;padding:0;border:0;outline:none;font-size:13px;line-height:inherit}.mk-menu-search-input::-ms-clear{display:none}body:not(.is-phone) .mk-menu-suggestions{width:100%;height:100%;overflow-y:auto;overflow-x:hidden;margin:4px -1px;max-height:var(--mk-menu-max-height)}.is-phone .mk-menu-suggestions{width:100%;flex:1;overflow-y:auto}.mk-menu-input{display:flex;gap:8px;align-items:center;margin:0 6px;padding:4px 8px}.mk-menu-input input{border:thin solid var(--mk-ui-border);background:var(--mk-ui-background-contrast)}.is-phone .mk-menu-option{padding:12px;background:none!important}.is-mobile .mk-menu-option:hover{background:none!important}.mk-menu-option{margin:0 6px;padding:6px 8px;border-radius:var(--mk-ui-radius-small);display:flex;flex-direction:row;align-items:center;gap:6px;cursor:var(--cursor);font-size:13px;justify-content:space-between}.mk-menu-option .mk-inline-button{border:thin solid var(--mk-ui-border);padding:4px!important}.mk-menu-options-inner{flex:1;display:flex;flex-direction:column;overflow:hidden;align-items:flex-start}.mk-menu-options-section{flex:1;display:flex;flex-direction:column;overflow:hidden}.mk-menu-options-description{color:var(--mk-ui-text-tertiary);font-size:12px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.mk-menu-option span{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mk-menu-option mark{text-decoration:underline;background:none;font-weight:600;border-radius:unset;margin:unset;padding:unset}.mk-menu-option:not(.mk-disabled):hover{cursor:pointer;background:var(--mk-ui-background-hover)}.mk-menu-option.mk-active{background:var(--mk-ui-background-hover)}.mk-menu-option.mk-disabled{opacity:.5;cursor:auto}.mk-menu-custom{padding:0!important;gap:0!important;background:none!important}.mk-menu-custom>.menu-item-title>div{width:100%}.mk-menu-custom .menu-item span:first-child{flex-grow:1}.mk-menu-custom .menu-item span:last-child{color:var(--mk-ui-text-secondary)}.mk-menu-custom li{border-radius:var(--mk-ui-radius-small);display:flex;flex-direction:row;align-items:center;gap:6px}.mk-menu-custom li>div{width:100%}@font-face{font-family:emoji;src:local("Apple Color Emoji"),local("Android Emoji"),local("Segoe UI"),local(EmojiSymbols),local(Symbola);unicode-range:U+1F300-1F5FF,U+1F600-1F64F,U+1F680-1F6FF,U+2600-26FF}.mk-sticker-menu .suggestion{width:240px;height:240px;display:flex;flex-wrap:wrap;align-content:flex-start;flex-direction:row}.mk-sticker-modal{display:flex;flex-wrap:wrap}.mk-sticker-modal .suggestion-item{width:30px;height:30px;display:flex;font-size:20px;gap:4px;align-items:center;padding:0;text-align:center;justify-content:center;font-family:emoji}.mk-sticker-filter{border:none;background:none;border-bottom:thin solid var(--mk-ui-border);width:100%;padding:8px 12px}.mk-sticker-menu .suggestion-item:hover{background:var(--mk-ui-background-hover)}.mk-image-modal{display:flex;flex-wrap:wrap}.mk-sticker{display:flex;height:var(--icon-size);align-items:center}.mk-sticker svg{height:var(--icon-size);width:var(--icon-size);stroke-width:var(--icon-stroke)}@keyframes slideInFromBottom{0%{transform-origin:top 50%;transform:translateY(50px);opacity:.5}to{transform:translateY(0);opacity:1}}@keyframes slideInFromTop{0%{transform-origin:top 50%;transform:translateY(-100%);opacity:.5}to{transform:translateY(0);opacity:1}}.mk-modal-actions{display:flex;justify-content:flex-end;gap:8px;margin-top:16px}.mk-input{background-color:var(--mk-ui-background);border:none;border-radius:0}.mk-border-bottom{border-bottom:1px solid var(--mk-ui-divider)}.mk-input-large[data-placeholder]:empty:before{content:attr(data-placeholder);color:var(--mk-ui-text-tertiary)}.mk-input-large{padding:16px 20px;font-size:14px}.mk-modal-header{display:flex;justify-content:space-between}.mk-modal-title{font-size:var(--font-ui-large);margin-bottom:.75em;font-weight:var(--font-semibold);text-align:left;line-height:1.3}.mk-modal:before,.mk-palette:before{content:"";position:absolute;inset:0;background:inherit;backdrop-filter:blur(10px) saturate(2);z-index:-1}.mk-palette,.mk-modal{display:flex;flex-direction:column;border-radius:12px;background-color:var(--mk-ui-background-blur);box-shadow:var(--shadow-l);border:thin solid var(--mk-ui-divider);z-index:1}.mk-palette{top:80px;width:700px;max-width:80vw;height:70vh;overflow:hidden;position:absolute}.mk-modal{padding:var(--size-4-4);position:relative;min-height:100px;width:var(--dialog-width);max-width:var(--dialog-max-width);max-height:var(--dialog-max-height);overflow:auto}.is-phone .mk-palette,.is-phone .mk-modal{bottom:0;max-width:100%;width:100%;max-height:100%;left:0;--mobile-height: 100vh;--prompt-bottom: 0px;--prompt-top: calc(var(--safe-area-inset-top) + var(--header-height) + var(--size-4-2));border-radius:var(--radius-l) var(--radius-l) 0 0;min-width:unset;margin-bottom:var(--prompt-bottom);margin-top:var(--prompt-top);box-shadow:none;top:0;height:calc(var(--mobile-height) - var(--prompt-top) - var(--prompt-bottom))}.is-phone .mk-palette-search{background:var(--mk-ui-background-input);border-radius:8px;border-bottom:none;padding:8px;margin:8px;display:flex}.mk-palette-search{display:flex;gap:8px;padding:12px}.mk-palette-search input{border:none;background:none}.mk-modal-wrapper{padding:0;-webkit-app-region:no-drag;background-color:var(--mk-ui-background-overlay);position:fixed;z-index:var(--layer-menu);user-select:none;overflow:hidden;width:100%;height:100%;top:0;left:0}.mk-modal-container{width:100%;height:100%;display:flex;align-items:center;justify-content:center}.mk-modal-message{text-overflow:ellipsis;width:100%;overflow:hidden;white-space:pre-wrap}.mk-modal-contents{display:flex;flex-direction:column;gap:16px;width:100%}.mk-modal-card{display:flex;flex-direction:column;gap:8px;padding:12px;border-radius:8px;background:var(--mk-ui-background);box-shadow:var(--mk-shadow-card)}.mk-modal-description{font-size:var(--font-ui-small);color:var(--mk-ui-text-secondary)}.mk-modal-items{display:flex;flex-direction:column;gap:8px}.mk-modal-item{display:flex;gap:8px}.mk-tab-group{display:flex;gap:4px;margin-bottom:8px}.mk-tab{padding:4px 12px;border-radius:8px;display:flex;--icon-size: 20px;border:thin solid transparent}.mk-tab svg{width:var(--icon-size);height:var(--icon-size)}.mk-tab:hover{background:var(--mk-ui-background-hover)}.mk-tab.mk-active{border:thin solid var(--mk-ui-border);background:var(--mk-ui-background-selected);color:var(--mk-color-ui-accent)}.mobile-toolbar-options-container{border-top:1px solid var(--mk-ui-divider)}.mk-blink-modal .mk-options-menu{display:grid!important;grid-template-columns:5fr 8fr;grid-template-rows:50px auto;height:var(--prompt-max-height);width:unset!important}.mk-blink-modal .mk-options-menu__search{width:100%;padding:0}.mk-blink-modal .mk-options-menu__suggestions li{margin:6px}.mk-space-modal{display:flex;flex-direction:column;height:100%}.mk-blink-modal .mk-options-menu__search input{padding:var(--size-4-6);background-color:var(--mk-ui-background);font-size:15px;border:none;height:40px;border-radius:0;border-bottom:1px solid var(--background-secondary)}.mk-blink-modal .mk-options-menu__selected{grid-column:1 / 3;grid-row:1;padding:0;margin-top:0}.mk-blink-modal .mk-options-menu__suggestions{padding:var(--size-4-3);grid-column:1;grid-row:2;max-height:unset;width:unset}.mk-blink-preview{position:relative;border-left:thin solid var(--mk-ui-divider);display:flex;flex-direction:column}.mk-blink-preview-title{padding:8px;display:flex}.mk-blink-preview-title span{flex:1}.mk-blink-preview-title button{padding:4px 8px;font-size:max(13px,1em);gap:8px;font-weight:var(--font-medium);background:rgba(var(--nav-item-background-active),.3);border:none;box-shadow:none;color:var(--mk-ui-text-tertiary);height:30px}.mk-blink-input-container{padding:14px;gap:8px;font-size:14px;display:flex;border-bottom:1px solid var(--mk-ui-divider);align-items:center}.mk-blink-options{border-top:1px solid var(--mk-ui-divider);background-color:var(--mk-ui-background-contrast)}.mk-blink-input{flex:1}.mk-blink-input[data-placeholder]:empty:before{content:attr(data-placeholder);color:var(--mk-ui-text-tertiary)}.mk-blink-preview-title button:hover,.mk-blink-preview-title button.is-active{color:var(--mk-ui-text-primary);background:var(--mk-ui-background-hover)}.mk-blink-properties{border-top:thin solid var(--mk-ui-border);padding:8px}.mk-query-group-type{display:flex;align-items:center}.mk-blink-properties-header{margin-bottom:6px}.mk-blink-preview>.mk-path-view{flex:1;padding:12px;min-height:0;overflow-y:scroll}.is-phone .mk-blink-suggester{flex-direction:column}.is-phone .mk-blink-suggester .mk-blink-preview{width:100%;height:50%;padding:0;margin:0;border-radius:0;transition:all .3s ease-in-out}.mk-flowspace-editor{padding:0!important}.mk-blink-filters{display:flex;width:100%;border-bottom:thin solid var(--mk-ui-border);padding:0 16px}.mk-blink-suggester{display:flex;flex:1;min-height:0}.mk-blink-suggester .mk-blink-suggestions{min-width:200px;overflow-y:auto;flex:1;height:auto;gap:4px;display:flex;flex-direction:column;align-items:stretch;padding:6px 0}.mk-blink-suggester .mk-blink-suggestion{padding:10px 8px;margin:0 6px;border-radius:var(--mk-ui-radius-small);display:flex;flex-direction:row;align-items:center;gap:12px;cursor:var(--cursor);font-size:13px;justify-content:space-between}.mk-blink-suggester .mk-blink-section{padding:4px;margin:0 6px;display:flex;flex-direction:row;align-items:center;cursor:var(--cursor);font-size:12px}.mk-blink-suggestion:hover,.mk-blink-suggestion.mk-active{background:var(--mk-ui-background-hover)}.mk-blink-suggestion-icon{width:24px;height:24px}.mk-blink-suggestion-icon svg{width:24px;height:24px}.mk-blink-suggestion-text{display:flex;flex-direction:column;flex:1}.mk-blink-suggester .mk-blink-suggestion .mk-blink-suggestion-title{flex:1;font-size:14px}.mk-blink-suggestion-description{color:var(--mk-ui-text-tertiary);font-size:12px}.mk-blink-suggestion-preview{color:var(--mk-ui-text-secondary);font-size:12px;text-overflow:ellipsis;display:-webkit-box;overflow:hidden;-webkit-line-clamp:1;-webkit-box-orient:vertical;white-space:pre-wrap}.mk-blink-suggester .mk-blink-preview{flex:1;margin:8px;border:thin solid var(--mk-ui-divider);padding:8px;border-radius:8px;box-shadow:var(--mk-shadow-card)}.mk-property-editor-context-title{display:flex;font-size:13px;font-weight:var(--font-normal);padding:8px 4px}.mk-property-editor-context-title span{flex:1}.mk-property-editor-context-tag{display:flex;gap:4px;align-items:center}.mk-property-editor{padding:0;display:flex;flex-direction:column;height:100%;gap:8px}.mk-property-editor-property{display:flex;padding:8px;font-size:13px;color:var(--mk-ui-text-secondary);gap:6px;align-items:center;background:var(--background-modifier-cover);border-radius:6px}.mk-property-editor-new:hover{color:var(--mk-ui-text-primary)}.mk-property-editor-new span{font-size:12px}.mk-property-editor-new{color:var(--mk-ui-text-tertiary);display:flex;flex-direction:column;padding:8px;border:thin solid var(--mk-ui-divider);border-radius:8px}.mk-property-editor-context-title{font-size:15px;font-weight:var(--font-semibold);line-height:var(--line-height-tight);padding:8px 4px;display:flex;align-items:center;gap:8px}.mk-property-editor-list{display:flex;flex-direction:column;gap:4px}.mk-property-editor-context-title span,.mk-property-editor-list span{font-size:12px;color:var(--mk-ui-text-tertiary);flex:1}.mk-path-explorer{display:flex;flex-direction:column;--file-context-spacing: 4px;height:100%;padding:12px;gap:4px}.mk-path-context-header{height:var(--mk-header-height);border-bottom:var(--mk-ui-divider);background-color:var(--mk-ui-background);z-index:1;position:relative;gap:8px;padding:0 var(--size-4-3)}.mk-path-context-label{display:flex;width:100%;flex-direction:column;align-items:flex-start}.mk-header .mk-path-context-file.mk-path-context-file-horizontal,.mk-space-title.mk-path-context-file-horizontal{gap:12px}.mk-path-context-file.mk-path-context-file-horizontal,.mk-space-title.mk-path-context-file-horizontal{display:flex;flex-direction:row;align-items:center}.markdown-source-view.mod-cm6 .cm-content .mk-header{padding-inline-start:0!important;text-indent:0!important}.mk-space-header .mk-path-icon-placeholder button,.mk-inline-context .mk-path-context-component .mk-path-icon-placeholder button{background:var(--label-color);border-radius:8px;padding:8px!important}.mod-right-split .mk-header,.mod-left-split .mk-header{display:none}body .markdown-reading-view .mk-path-context-label .mk-inline-title.inline-title,body .markdown-source-view.mod-cm6.mod-cm6.is-readable-line-width .mk-inline-title.inline-title{max-width:100%;padding-bottom:0;margin-left:0!important;margin-top:8px;padding-top:0!important;margin-bottom:0;width:100%}.mk-inline-title{margin-top:8px;margin-bottom:0}.mk-path-context-component .mk-tag-selector{margin-top:0;margin-bottom:0;padding:0}.mk-path-context-component{display:flex;flex-direction:column;gap:8px;align-items:flex-start}.mk-path-context-properties{display:flex;flex-direction:column;gap:8px}.mk-backlinks{padding-top:8px}.mk-path-backlink-title .mk-collapse{width:24px!important;height:24px!important}.mk-path-backlink-title .mk-collapse svg{transform:rotate(90deg)}.mk-path-backlink-title .mk-collapse.mk-collapsed svg{transform:rotate(0)}.mk-path-backlink-title .mk-collapse:hover{background:var(--mk-ui-background-hover)!important}.mk-path-context-row{display:flex;align-items:flex-start;gap:2px}.mk-path-context-row-new{display:flex;align-items:center;gap:2px}.mk-path-context-stacked{flex-direction:column}.mk-path-context-stacked>.mk-path-context-field{max-width:100%;min-width:100%}.mk-path-context-row>span{flex:1}.mk-path-context-row .mk-cell-option{width:unset;align-items:flex-start}.mk-path-context-field-key{padding:4px;display:inline-block;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.mk-path-context-field-space{display:flex;justify-content:center;width:18px;height:18px;border-radius:9px;position:absolute;align-items:center;top:12px;left:10px;background:var(--mk-ui-background)}.mk-path-context-field-space svg{width:12px;height:12px;color:var(--mk-ui-text-tertiary)}.mk-path-context-field-space:hover{opacity:1}.mk-path-context-field-icon{--icon-size: 18px;width:var(--icon-size);height:var(--icon-size);display:flex;align-items:center;text-align:center;justify-content:center}.mk-path-explorer .mk-path-context-row{flex-direction:column}.mk-path-explorer .mk-path-context-value{margin-left:24px}.mk-path-explorer .mk-path-context-field{max-width:120px;min-width:120px}.mk-path-context-new,.mk-path-context-field{max-width:140px;min-width:140px;font-size:14px;color:var(--mk-ui-text-tertiary);min-height:24px;gap:8px;display:flex;border-radius:4px;align-items:center;position:relative}.mk-bullet{min-height:24px;display:flex;align-items:center;padding:8px 5px}.mk-bullet:after{display:block;content:" ";width:5px;height:5px;border-radius:50%;background-color:var(--bullet-color);transition:transform 75ms ease-out}.mk-path-explorer .mk-cell-text{background:var(var(--mk-ui-active-hover))!important}.mk-path-preview{display:flex;max-width:100px;overflow-x:hidden;align-items:flex-start;mask-image:-webkit-gradient(linear,left 90%,right bottom,from(rgba(0,0,0,1)),to(rgba(0,0,0,0)))}.mk-path-info{display:flex;border:thin solid var(--mk-ui-border);border-radius:8px;padding:2px 10px;align-items:center;gap:4px}.mk-path-preview .mk-path{overflow:visible;--icon-size: 12px;--icon-container-size: 16px;padding:2px 4px;gap:2px}.mk-path-context-value{font-size:13px;flex-grow:1;display:flex;align-items:center;min-height:24px;flex-wrap:wrap;gap:8px}.mk-path-context-new:hover,.mk-path-context-field:hover{color:var(--mk-ui-text-primary)}.mk-path-context-value .mk-cell-text{background:none!important}.mk-path-context-value .mk-cell-text:hover{background:var(var(--mk-ui-active-hover))!important;border-radius:4px}.mk-path-context-value .mk-cell-option-new{visibility:visible!important}.mk-path-context-value input[type=text],.mk-path-context-value input[type=number]{width:130px;padding:0;border:none;border-radius:0;height:24px}.mk-path-context-value input[type=text]:focus,.mk-path-context-value input[type=number]:focus{box-shadow:none!important}.mk-path-explorer .mk-path-context-title{--icon-size: 14px;font-size:14px}.mk-path-backlinks{display:flex;flex-direction:column;width:100%}.mk-path-context-title svg{width:var(--icon-size);height:var(--icon-size)}.mk-path-context-title div{display:flex}.mk-path-context-title{display:flex;--icon-size: 18px;font-size:18px;font-weight:var(--font-medium);align-items:center;gap:8px;position:relative}.mk-path-backlink-title:hover{color:var(--mk-ui-text-primary)}.mk-path-backlink-title .mk-inline-button svg{color:var(--mk-ui-text-tertiary)}.mk-path-context-folder{padding:0!important;color:var(--color-accent-1);background:none!important;box-shadow:none!important;height:unset!important}.mk-path-explorer .mk-tag-selector{padding:0!important}.mk-space-icon{display:flex;padding-right:2px}.mk-path-context-backlink{background:var(--mk-ui-background);padding:8px}.mk-path-explorer .mk-path-context-backlink{border:thin solid var(--mk-ui-border);border-radius:8px;box-shadow:var(--mk-shadow-card)}.mk-path-context-backlink .cm-content{padding-bottom:0!important}.mk-space-banner{position:absolute;top:0;left:0;right:0;width:100%;max-width:100vw;user-select:none;height:var(--mk-banner-height);background-size:cover;background-position-y:center}.mk-space-banner-buttons:hover,.mk-space-banner:hover+.mk-space-banner-buttons{opacity:1}.mk-space-banner-buttons{display:flex;align-items:center;gap:8px;z-index:0;position:absolute;right:8px;top:8px;opacity:0}.mk-space-banner img{height:var(--mk-banner-height);width:100%;object-fit:cover;opacity:1!important}.mk-path-context-file-horizontal .mk-fold{bottom:8px}.markdown-source-view:not(.is-live-preview)>.cm-editor>.cm-scroller>.cm-sizer>.mk-inline-context{display:none}.mk-inline-context{gap:8px;display:flex;flex-direction:column;border-top:none!important;margin-bottom:8px}.mk-space-header:hover .mk-fold,.mk-inline-context:hover .mk-fold{opacity:1!important}.markdown-source-view.mod-cm6 .mk-has-backlinks img.cm-widgetBuffer{display:none!important}.mk-inline-context .mk-path-context-component{gap:8px}.markdown-source-view.mod-cm6 .cm-content .mk-note-footer{margin:16px 0!important;padding-top:16px}.mk-note-footer{position:relative}.mk-note-footer .mk-fold{top:0}.mk-header .mk-path-context-component div{pointer-events:all}.mk-inline-context-enabled .markdown-source-view.is-live-preview>.cm-editor>.cm-scroller>.cm-sizer>.inline-title{display:none}.mk-inline-title:empty:before{content:attr(data-ph);color:var(--mk-ui-text-tertiary);position:absolute}.mk-note-header .inline-title{display:flex!important;margin-bottom:0}.mk-header-title{max-width:var(--file-line-width);padding:0 var(--file-margins);margin-left:auto;margin-right:auto;margin-top:20px}.mk-header-title.mk-header-has-banner{margin-top:-34px}.workspace-leaf-content[data-type=mk-ever-view] .view-header{display:none}.workspace-leaf-content[data-type=mk-space] .view-content,.workspace-leaf-content[data-type=mk-ever-view] .view-content,.workspace-leaf-content[data-type=make-context-view] .view-content,.workspace-leaf-content[data-type=mk-html-view] .view-content,.workspace-leaf-content[data-type=mk-space-fragment] .view-content,.workspace-leaf-content[data-type=mk-uri-view] .view-content{padding:0}.workspace-leaf-content[data-type=mk-html-view] .view-content{overflow:hidden}.workspace-leaf-content[data-type=mk-space-fragment]{border:0!important}.markdown-source-view.mod-cm6 .cm-content>.mk-header{--mk-header-height: 0px;gap:16px;display:flex;flex-direction:column;font-size:15px;contain:unset!important;position:inherit}.mk-inline-context-enabled .markdown-reading-view .mod-header>.inline-title,.mk-inline-context-enabled .markdown-reading-view .frontmatter,.mk-inline-context-enabled .markdown-reading-view .frontmatter-container{display:none}.mk-inline-context .mk-path-context-component .mk-spacer,.markdown-source-view.mod-cm6 .cm-content>.mk-header .mk-spacer,.mk-space-view .mk-spacer,.markdown-reading-view .mk-spacer{min-height:var(--mk-header-height)!important;pointer-events:none}.mk-path-explorer .inline-title{font-size:18px;margin-bottom:0}.mk-path-context-component .mk-inline-button{opacity:.5;position:relative}.mk-path-context-component:hover .mk-inline-button{opacity:1!important}.mk-path-icon svg{width:var(--icon-size);height:var(--icon-size)}.mk-context{--icon-size: 14px;--icon-container-size: 16px;display:flex;align-items:center;padding:4px 12px;gap:4px;border-radius:18px;white-space:nowrap;overflow:hidden;font-size:14px}.mk-context.mk-active{background:var(--mk-ui-background-hover)}.mk-path{--icon-size: 16px;--icon-container-size: 20px;display:flex;align-items:center;padding:2px 4px;gap:4px;border-radius:4px;white-space:nowrap;overflow:hidden;color:var(--mk-ui-text-secondary)}.mk-path span{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;display:inline-block}.mk-context:hover{background:var(--mk-ui-background-hover)}.mk-path:hover{text-decoration:underline;cursor:pointer}.mk-space-header .mk-space-title.mk-path-context-file-horizontal .mk-path-icon,.mk-inline-context .mk-path-context-file.mk-path-context-file-horizontal .mk-path-icon{--icon-size: 32px;margin-bottom:0}.mk-space-header .mk-path-context-file-horizontal .mk-path-icon,.mk-inline-context .mk-path-context-file-horizontal .mk-path-icon{--icon-size: 32px}.mk-header-icon{--icon-size: 40px;--icon-container-size: 48px;display:flex;align-items:flex-end;gap:8px;z-index:1}.mk-header-label-actions{display:flex;gap:12px;align-items:center}.mk-inline-context:hover .mk-header-label-actions .mk-inline-button,.mk-space-header:hover .mk-header-label-actions .mk-inline-button{opacity:1}body:not(.is-mobile) .mk-header-label-actions .mk-inline-button{opacity:0;transition:all .2s ease}.mk-space-header,.mk-inline-context,.mk-path-context-label .mk-path-icon svg.svg-icon{--icon-size: 40px}.mk-ever-view{display:flex;flex-direction:column;height:100%}.mk-ever-view-header{display:flex;padding:8px 12px;gap:4px}.mk-ever-view-header-title{display:flex;flex-direction:column;flex:1}.mk-ever-view-title{font-size:16px;font-weight:500;color:var(--mk-ui-text-primary)}.mk-ever-view-filters{padding:4px 8px}.mk-ever-view-contents{flex:1;overflow:auto}.mk-ever-view-count{font-size:12px;color:var(--mk-ui-text-secondary)}.mk-ever-view-filter{padding:8px}.mk-hide-tabs .mod-left-split .mod-top-left-space .workspace-tab-header-container-inner{visibility:hidden}.is-phone .mod-root .workspace-tabs:not(.mod-visible){display:flex!important}.mk-mobile-sidepanel.is-mobile.mk-hide-ribbon:not(.mk-spaces-right) .workspace-drawer.mod-left .workspace-drawer-inner,.mk-mobile-sidepanel.is-mobile.mk-hide-ribbon.mk-spaces-right .workspace-drawer.mod-right .workspace-drawer-inner{padding-left:0!important}.mk-hide-vault-selector:not(.is-mobile) .workspace-sidedock-vault-profile{display:none!important}.mk-hide-ribbon .workspace-ribbon{display:none}.mk-hide-ribbon.is-hidden-frameless:not(.is-fullscreen) .workspace-tabs.mod-top-left-space .workspace-tab-header-container:before{width:calc(var(--frame-left-space) + var(--ribbon-width))}.mk-hide-ribbon.is-hidden-frameless:not(.is-fullscreen) .workspace-tabs.mod-top-left-space .workspace-tab-header-container{padding-left:calc(var(--frame-left-space) + var(--ribbon-width))}.mk-mobile-sidepanel.is-mobile.mk-hide-ribbon .workspace-drawer-ribbon{display:none}.mk-mobile-sidepanel.is-mobile:not(.mk-spaces-right) .workspace-drawer.mod-left .workspace-drawer-inner .workspace-drawer-header,.mk-mobile-sidepanel.is-mobile.mk-spaces-right .workspace-drawer.mod-right .workspace-drawer-inner .workspace-drawer-header{padding-left:0!important}.mk-mobile-sidepanel.is-tablet:not(.mk-spaces-right) .workspace-drawer.mod-left .workspace-drawer-inner .workspace-drawer-header,.mk-mobile-sidepanel.is-tablet.mk-spaces-right .workspace-drawer.mod-right .workspace-drawer-inner .workspace-drawer-header{padding-right:0!important}.mk-mobile-sidepanel.is-phone:not(.mk-spaces-right) .workspace-drawer.mod-left .workspace-drawer-inner .workspace-drawer-header,.mk-mobile-sidepanel.is-phone.mk-spaces-right .workspace-drawer.mod-right .workspace-drawer-inner .workspace-drawer-header{padding-right:0!important;padding-top:0}.mk-mobile-sidepanel.is-mobile:not(.mk-spaces-right) .workspace-drawer.mod-left .workspace-drawer-active-tab-header,.mk-mobile-sidepanel.is-mobile.mk-spaces-right .workspace-drawer.mod-right .workspace-drawer-active-tab-header{display:none}.mk-mobile-sidepanel.is-mobile .workspace-drawer.mod-left .workspace-drawer-inner .mod-settings,.mk-mobile-sidepanel.is-mobile.mk-spaces-right .workspace-drawer.mod-right .workspace-drawer-inner .mod-pin,.mk-mobile-sidepanel.is-mobile:not(.mk-spaces-right) .workspace-drawer.mod-left .workspace-drawer-inner .mod-pin{display:none}.is-mobile .mk-sidebar .mk-path-icon button{font-size:16px;margin:0;height:24px;width:24px}body.mk-mobile-sidepanel.is-mobile .sidebar-toggle-button{display:flex!important}.mk-mobile-sidepanel.is-mobile:not(.mk-spaces-right) .workspace-drawer.mod-left .workspace-drawer-header-icon,.mk-mobile-sidepanel.is-mobile.mk-spaces-right .workspace-drawer.mod-right .workspace-drawer-header-icon{position:absolute;right:20px;top:12px;z-index:100}.mk-mobile-sidepanel.is-phone .workspace-drawer.mod-left .workspace-drawer-header-icon{top:20px}.mk-mobile-sidepanel.is-mobile .workspace-drawer.mod-left{border-top-right-radius:0;border-bottom-right-radius:0}.mk-sidebar{display:flex;flex-direction:column;height:100%}.mk-path-tree-focus{display:flex;flex-direction:column;align-items:center;gap:8px;justify-content:center;height:100%}.mk-path-tree-focus .mk-focuses-item{background:var(--mk-ui-background)}.mk-path-tree-focus input{width:60%;padding:8px;border:none;border-radius:4px;text-align:center}.mk-button-group{display:flex;gap:8px}.mk-path-tree-empty{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:12px;gap:8px}.mk-empty-state-description{font-size:14px;color:var(--mk-ui-text-secondary);text-align:center}.mk-empty-state-title{font-size:16px;color:var(--mk-ui-text-primary)}.mk-path-tree{flex:1;overflow:hidden}.mk-context-tree .tree-item{padding:4px}.mk-context-tree .tree-item-self{margin-left:var(--spacing);align-items:center;padding:2px}.mk-context-tree .mk-tree-wrapper{padding:0!important}.tag-container{flex:1}.mk-button-blink{padding:8px 12px!important}.mk-search{display:flex;padding:8px;margin:4px 12px 4px 0}.mk-search svg{width:16px;height:16px}.mk-waypoint{display:flex}.mk-waypoint-new{display:flex;align-items:center;color:var(--mk-ui-text-secondary);opacity:.5}.mk-waypoint-new:hover{opacity:1}.mk-focuses .mk-focuses-item{display:flex;justify-content:center}.is-mobile .mk-focuses{box-shadow:var(--mk-shadow-card);padding-top:12px}.is-mobile .mk-focuses>div{gap:8px;overflow:initial}.mk-focuses{display:flex;overflow:auto;padding:12px 12px 4px}.mk-focuses-inner{flex-wrap:wrap;gap:4px;display:flex;justify-content:center}.mk-sidebar-expanded{flex-grow:1}.mk-waypoint:hover .mk-focuses-item:not(.mk-active){background:var(--nav-item-background-hover)}.mk-focuses-item{color:var(--mk-ui-text-secondary);height:30px;min-width:30px;padding:6px;border-radius:50%;display:flex;align-items:center;gap:4px;text-align:center;color:var(--icon-color);transition:transform .25s ease}.mk-focuses-item svg,.mk-waypoint-new svg{width:var(--icon-size);height:var(--icon-size)}.is-mobile .mk-focuses-item{width:40px;height:40px;border-radius:20px}.mk-focuses-item.mk-active{--label-color: var(--mk-ui-background-selected);color:var(--text-n);background-color:var(--label-color);opacity:1}.workspace .mod-root .workspace-tab-header[data-type=markdown] .workspace-tab-header-inner-icon{display:flex}.workspace .mod-root .workspace-tab-header[data-type=markdown] .workspace-tab-header-inner-icon svg{width:18px;height:18px}.workspace-tab-header-inner-icon svg,.workspace-tab-header-inner-icon img{width:var(--icon-size);height:var(--icon-size)}.mk-tree-node{position:absolute;top:0;left:0;width:100%;height:var(--row-height);transform:translateY(var(--node-offset))}.mk-tree-wrapper{box-sizing:border-box;margin-left:6px;margin-bottom:1px;display:flex;align-items:center!important;padding:0 6px;position:relative;height:var(--spaceRowHeight);gap:4px}.nav-file{overflow-y:inherit!important}.is-mobile .mk-tree-wrapper{padding-top:6px;padding-bottom:6px}.mk-tree-wrapper>div{display:flex;width:100%;transition:transform .2s ease}.mk-focuses{position:relative}.mk-focuses>.mk-indicator{border-radius:8px;outline:2px solid var(--mk-ui-active)}.mk-focuses>.mk-focuses-item{transition:transform .2s ease}.mk-tree-wrapper>.mk-indicator-top:before{content:" ";display:block;position:absolute;height:2px;border-radius:1px;background:var(--mk-ui-active);width:calc(100% - var(--spacing));left:var(--spacing);top:0%}.mod-rtl.mk-folder-lines .mk-tree-item:before{display:block;content:" ";width:1px;height:var(--childrenCount);border-left:1px solid var(--nav-indentation-guide-color);position:absolute;top:34px;right:13px}body:not(.mod-rtl).mk-folder-lines .mk-tree-item:before{display:block;content:" ";width:1px;height:var(--childrenCount);border-left:1px solid var(--nav-indentation-guide-color);position:absolute;top:34px;left:13px}.mk-tree-wrapper .mk-indicator-row{border-radius:8px;outline:2px solid var(--mk-ui-active)}.mk-tree-wrapper.mk-clone{display:inline-block;pointer-events:none;padding:5px 0 0 10px}.mk-tree-wrapper.mk-clone.mk-tree-item{--vertical-padding: 5px;padding-right:24px;box-shadow:0 15px 15px #2221511a}.mk-tree-wrapper.mk-ghost{opacity:.5}.mk-tree-section{margin-top:4px;height:var(--spaceSectionHeight)}.mk-tree-section .mk-tree-item{height:var(--spaceRowHeight);padding-left:4px}.mk-tree-section .is-active.mk-tree-item,.mk-tree-section .mk-tree-item:hover,.mk-tree-new:hover{opacity:1}.mk-tree-section .mk-collapse{--icon-size: 12px}.mk-tree-section .mk-tree-text{font-size:14px;display:flex;gap:4px;flex-grow:0!important}.mk-sidebar:hover .mk-tree-new{opacity:.5}body:not(.is-mobile) .mk-tree-new{opacity:0}.mod-rtl .mk-tree-item{margin-left:0!important;margin-right:var(--spacing)!important}.mk-tree-item{--icon-size: 16px;--icon-container-size: 18px;margin-left:var(--spacing)!important;flex-direction:row!important;--vertical-padding: 2px;flex-grow:1;position:relative;align-items:center!important;padding:var(--vertical-padding) 2px;min-width:0;margin-right:4px;margin-bottom:2px;display:flex;border-radius:4px;color:var(--mk-ui-text-secondary);font-size:13px;line-height:1.3;gap:2px;height:var(--spaceRowHeight)}.is-mobile .mk-tree-item{height:38px;gap:8px}.is-mobile .mk-tree-item:hover{background-color:unset!important}.workspace-leaf:not(.mod-active) .is-selected{background:var(--nav-item-background-active)!important}.workspace-leaf.mod-active .is-highlighted{background:var(--nav-item-background-active)!important}.mk-inline-button,.mk-folder-buttons button,.mk-folder-buttons div{background:none;border:0;box-shadow:none;margin:0;height:24px;width:24px;padding:0!important}.is-mobile .mk-tree-wrapper .mk-folder-buttons button{margin-left:8px}body:not(.is-mobile) .mk-folder-buttons button:hover{background:var(--nav-item-background-hover)}.mk-path-icon{width:var(--icon-container-size);height:var(--icon-container-size);display:flex;font-family:emoji;align-items:center}.mk-path-icon svg,.mk-path-icon img{width:var(--icon-size);height:var(--icon-size);color:var(--icon-color)!important}.mk-path-icon button{background:var(--label-color);border:0;box-shadow:none;height:var(--icon-container-size);width:var(--icon-container-size);padding:0!important}.is-mobile .mk-path-icon{width:unset}body:not(.is-mobile) .mk-tree-wrapper .mk-drag-handle{visibility:hidden;margin-right:4px}body:not(.is-mobile) .mk-tree-wrapper:hover .mk-drag-handle{visibility:visible}.is-mobile .mk-folder-buttons{display:flex}.mk-folder-buttons div{align-items:center;justify-content:center}body:not(.is-mobile) .mk-tree-wrapper .mk-path-link{opacity:0}body:not(.is-mobile) .mk-tree-wrapper:hover .mk-path-link{opacity:1}body:not(.is-mobile) .mk-tree-wrapper .mk-folder-buttons button,body:not(.is-mobile) .mk-tree-wrapper .mk-folder-buttons div{display:none}body:not(.is-mobile) .mk-tree-wrapper:hover .mk-folder-buttons button,body:not(.is-mobile) .mk-tree-wrapper:hover .mk-folder-buttons div{display:flex}body:not(.is-mobile) .mk-tree-wrapper .mk-folder-buttons{display:flex}.mk-tree-item .mk-folder-buttons svg{width:16px;height:16px;color:var(--mk-ui-text-secondary)}.is-mobile .mk-tree-wrapper svg{color:var(--mk-ui-text-secondary);width:20px;height:20px}.is-mobile .mk-tree-wrapper .mk-path-icon svg{width:18px;height:18px;color:var(--mk-ui-text-tertiary)}.is-mobile .mk-tree-text{font-size:16px;padding-left:0}.mk-tree-text{padding:.15rem 4px;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;font-size:13px;display:inline-block;overflow-wrap:anywhere}.mk-tree-span{flex:1!important}.mk-path-link{vertical-align:middle}.mk-path-link svg{width:12px;height:10px;transform:rotate(45deg)}.mk-disable-interaction{pointer-events:none}.mk-disable-selection,.mk-clone .mk-tree-text{user-select:none;-webkit-user-select:none}.workspace-leaf-content[data-type=mk-path-view]>.view-content{padding:0!important}.is-mobile .mk-collapse{min-width:20px!important;width:20px!important;height:20px!important}.mk-collapse{background:none!important;border:0;box-shadow:none!important;padding:0!important;min-width:16px!important;width:16px!important;height:16px!important;opacity:.4;margin-right:2px}.mk-collapse:hover{background:none!important;opacity:1}.mk-collapse svg{transform:rotate(90deg);transition:transform .25s ease;width:12px;height:12px;margin:2px}.mod-rtl .mk-collapse.mk-collapsed svg{transform:rotate(180deg)}.mk-collapse.mk-collapsed svg{transform:rotate(0)}.mk-collapse.mk-collapsed-plus svg{transform:rotate(45deg)}.mk-tree-item.is-active:not(.clone){--icon-color: var(--nav-item-color-active);color:var(--nav-item-color-active);background:var(--nav-item-background-active)}.mk-icon-menu{transform:translate3d(-500px,0,0);z-index:var(--layer-menu)}.mk-icon-menu .menu{position:static!important;padding:0!important}.mk-tree-empty{padding-left:var(--spacing);padding-top:4px;padding-bottom:4px;font-size:13px;color:var(--mk-ui-text-tertiary)}.mk-progress-bar{height:1px;width:100%;overflow:hidden}.mk-progress-bar-value{width:100%;height:100%;background-color:var(--mk-ui-active);animation:indeterminateAnimation 1s infinite linear;transform-origin:0% 50%}@keyframes indeterminateAnimation{0%{transform:translate(0) scaleX(0)}40%{transform:translate(0) scaleX(.4)}to{transform:translate(100%) scaleX(.5)}}.mk-space-editor-modal{height:var(--dialog-max-height)}.mk-space-editor-container{display:flex;flex-direction:column;overflow:hidden;flex:1}.mk-space-editor{display:flex;flex-direction:column;margin-bottom:20px;gap:12px;flex:1;overflow:hidden}.mk-space-editor-appearance .mk-path-icon,.mk-space-editor-appearance .mk-path-icon button,.mk-space-editor-appearance .mk-path-icon svg{width:48px;height:48px;font-size:36px}.mk-space-editor-appearance .mk-path-icon svg{padding:6px}.mk-space-query{display:flex;flex-direction:column;height:100%}.mk-space-editor-context-list{display:flex;margin-bottom:8px}.mk-space-editor-section{display:flex;flex-direction:column;gap:8px;border-bottom:thin solid var(--mk-ui-divider)}.mk-space-editor-header .mk-button-new{--icon-size: 16px;padding:8px}.mk-space-editor-header span{flex:1}.mk-space-editor-contents{display:flex;flex-direction:column;gap:4px;overflow:scroll;flex:1}.mk-space-editor-contents .mk-path{overflow:visible}.mk-space-editor-controls{display:flex;justify-content:space-between}.mk-space-editor-links{display:flex;flex-direction:column;align-items:flex-start;gap:8px;margin-bottom:8px;font-size:13px;padding:8px;background:var(--mk-ui-background-contrast);box-shadow:var(--mk-shadow-card);border:thin solid var(--mk-ui-border);border-radius:10px}.mk-space-editor-links-header{display:flex;gap:8px;align-items:center}.mk-space-editor-links span{flex:1}.mk-space-editor-smart{display:flex;flex-direction:column;align-items:flex-start;gap:8px;margin-bottom:8px;font-size:13px;padding:8px;background:var(--mk-ui-background-contrast);box-shadow:var(--mk-shadow-card);border:thin solid var(--mk-ui-border);border-radius:10px}.mk-space-editor-smart-header{display:flex;gap:8px;align-items:center}.mk-space-editor-smart .mk-path{background:rgba(var(--mono-rgb-100),.025)}.mk-space-editor-smart .mk-path:hover{background:var(--mk-ui-active-hover)}.mk-space-context-view{display:flex;flex-direction:column;width:100%;gap:16px}.mk-space-editor-smart .mk-spacer{flex:1}.mk-space-editor-smart .mk-space-editor-smart-join{width:100%;border-bottom:thin solid var(--mk-ui-divider);padding-bottom:8px}.mk-space-editor-smart .mk-space-editor-smart-join-header{display:flex;gap:8px;align-items:center;flex-wrap:wrap}.mk-space-editor-smart .mk-query{width:100%;gap:8px;flex-direction:row;align-items:center}.mk-space-editor-smart .mk-query-filters{background:var(--mk-ui-background-contrast);padding:4px 8px}.mk-space-editor-smart .mk-query-group{border:thin solid var(--mk-ui-border);border-radius:8px}.mk-space-editor-smart .mk-query-group-type{width:24px;padding:4px}.mk-space-editor-appearance{display:flex;width:100%;gap:16px;align-items:center}.mk-space-editor-input input{font-weight:var(--inline-title-weight);font-size:var(--inline-title-size);line-height:var(--inline-title-line-height);font-style:var(--inline-title-style);font-variant:var(--inline-title-variant);font-family:var(--inline-title-font);color:var(--inline-title-color);background:none;outline:none;border:none}.mk-space-editor-smart span{text-wrap:nowrap}.mk-space-context-bar{display:flex;gap:1px;align-items:center;margin-top:8px;font-size:14px;border-radius:8px;overflow:hidden;flex-shrink:0}.mk-space-context-bar-section:empty{display:none!important}.mk-space-context-bar-section .mk-toolbar-button{height:26px}.mk-space-context-bar .mk-space-context-bar-section{display:flex;padding:2px 4px;gap:4px;align-items:center;background:var(--mk-ui-background-contrast);font-size:12px;color:var(--mk-ui-text-secondary)}.mk-space-context-bar .mk-space-context-bar-section>div{padding:4px;text-wrap:nowrap}.mk-space-context-bar .mk-spacer{flex:1}.mk-day-view{display:flex;flex:1}.mk-day-view-content{position:relative;width:100%;height:100%;box-sizing:border-box}.mk-day-view-gutter{width:50px;max-width:50px}.mk-day-view-container{display:flex;flex:1;width:100%;height:100%;flex-direction:column;position:relative}.mk-day-view-hour{position:relative;width:100%;display:flex;height:var(--hour-height);border-bottom:1px solid var(--mk-ui-background-contrast);box-sizing:border-box;text-align:center}.mk-day-view-all-day{position:relative;width:100%;display:flex;border-bottom:1px solid var(--mk-ui-border);box-sizing:border-box;text-align:center}.mk-day-view-hour-current{position:absolute;width:100%;display:flex;height:1px;border-bottom:1px solid var(--mk-color-red);box-sizing:border-box;text-align:center;background:var(--mk-ui-active);z-index:2}.mk-calendar-header{display:flex;flex:1;width:100%;gap:8px;position:relative;margin-bottom:8px}.mk-calendar-header span{flex:1}.mk-month-grid{display:flex;flex-direction:column;width:100%;height:100%;gap:1px}.mk-calendar-header-title span{font-weight:var(--font-medium);margin-right:4px}.mk-calendar-header-title{font-weight:var(--font-light);font-size:18px;padding:4px}.mk-calendar-header svg{width:18px;height:18px}.mk-calendar-header button{background:none;box-shadow:none;border:none;width:var(--icon-container-size);height:var(--icon-container-size);padding:0}.mk-month-week,.mk-month-header{display:flex;flex:1;width:100%;gap:8px;position:relative}.mk-month-header>div{display:flex;flex:1;width:100%;gap:1px;position:relative;border-bottom:thin solid var(--mk-ui-divider);padding:4px}.mk-month-day.mk-inactive{color:var(--mk-ui-text-tertiary)}.mk-month-day{position:relative;display:flex;flex-direction:column;flex:1;align-items:flex-start;font-size:12px;height:140px;padding:4px;overflow-y:clip}.mk-month-day-number{display:flex;align-items:center;justify-content:center;font-size:14px}.mk-month-day.mk-today .mk-month-day-number{color:var(--mk-ui-active)}.mk-week-event{position:absolute}.mk-day-block-time{display:flex;justify-content:flex-end;flex:1;font-size:10px;padding-right:2px;color:var(--mk-ui-text-primary)}.mk-month-event{position:absolute;box-sizing:border-box;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;display:flex;padding:2px;font-size:12px;height:20px;background:var(--block-bg-color);border-radius:4px}.mk-month-event .mk-path{color:var(--block-text-color)}.mk-month-event .mk-path,.mk-week-view-all-day-cell .mk-path{font-size:12px;padding:0 2px;gap:2px}.mk-week-view{display:flex;width:100%;flex-direction:column}.mk-week-view-header{display:flex;flex:1;width:100%}.mk-week-view-header div{display:flex;flex:1;align-items:center;justify-content:center;padding:4px}.mk-week-view-all-day{display:flex;width:100%;position:relative;gap:8px}.mk-week-view-all-day-cell{position:relative;flex:1;border-top:1px solid var(--mk-ui-border);border-bottom:1px solid var(--mk-ui-border)}.mk-day-view-all-day .mk-day-view-hour{display:flex;flex-direction:column;height:auto!important;gap:2px;padding:2px}.mk-day-block-repeat{padding:2px;color:var(--mk-ui-active)}.mk-day-block-repeat:hover{background:var(--mk-ui-background-hover)}.mk-day-block-repeat-hover{visibility:hidden;color:var(--mk-ui-text-tertiary)}.mk-day-block-inner:hover .mk-day-block-repeat-hover{visibility:visible}.mk-day-block-time{display:flex;align-items:center;gap:4px}.mk-day-block .mk-path{padding:0}.mk-day-block-time div{display:flex}.mk-day-block-time svg{width:12px;height:12px}.mk-day-view-all-day .mk-week-event{position:relative!important;background:var(--block-bg-color);border-radius:8px;font-size:12px}.mk-week-view-all-day-cell .mk-week-event{background:var(--block-bg-color);border-radius:8px}.mk-week-view-content{display:flex;flex:1;width:100%;overflow:hidden;position:relative;gap:8px}.mk-day-view-hour-title{font-size:10px;padding-right:8px;color:#666;position:relative;width:100%;display:flex;height:var(--hour-height);justify-content:flex-end}.mk-day-view-hour-title span{font-size:14px;color:var(--mk-ui-text-primary);margin-right:4px}.mk-day-block{position:absolute;box-sizing:border-box;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;padding:2px;font-size:12px;min-height:40px}.mk-day-block-content{max-height:100%;overflow:hidden;display:flex;flex-direction:column;align-items:flex-start}.mk-day-block-inner{position:relative;width:100%;height:100%;padding:4px;border-radius:4px;display:flex;gap:4px;background:var(--mk-ui-background-contrast);align-items:flex-start}.mk-day-block-inner span{flex:1}.mk-day-block-inner-indicator{height:100%;width:2px;border-radius:4px;background:var(--block-color)}.mk-day-handle-n{position:absolute;top:0;left:0;right:0;height:4px;cursor:ns-resize}.mk-day-handle-s{position:absolute;bottom:0;left:0;right:0;height:4px;cursor:ns-resize}.mk-f-edit{z-index:28;outline:thin solid var(--mk-ui-border-accent)}.mk-f-editable:not(.mk-f-edit):hover{z-index:28;outline:2px solid var(--mk-ui-border-accent);outline-offset:-2px}.mk-f-disabled{opacity:.5}.mk-frame-edit[data-type=column]{align-items:flex-start}.mk-frame-edit[data-type=new]{min-width:50px}.mk-frame-edit.mk-selected{background:var(--mk-ui-background-active);border-radius:4px}.mk-frame-edit.mk-layout-row{overflow:clip;overflow-clip-margin:28px}.mk-frame,.mk-frame-edit{--line-count: 1}.mk-f-root-label{position:absolute;padding:4px;border-radius:4px;font-size:13px;z-index:29;top:-22px;background:var(--mk-background-blur);display:none}.mk-f-root:hover>.mk-f-root-label{display:flex}.mk-frame[data-type=icon]{flex-grow:0;flex-shrink:0}.mk-frame[data-type=image]{display:flex}.mk-frame[data-type=listItem]:empty,.mk-frame[data-type=frame]:empty,.mk-frame[data-type=icon]:empty,.mk-frame[data-type=text]:empty,.mk-frame[data-type=image]:empty,.mk-frame-edit[data-type=icon]:empty,.mk-frame-edit[data-type=text]:empty,.mk-frame-edit[data-type=image]:empty{display:none}.mk-frame.mk-icon-size-s{--icon-size: 18px }.mk-frame.mk-icon-size-m{--icon-size: 24px }.mk-frame.mk-icon-size-l{--icon-size: 48px }.mk-layout-row{display:flex}.mk-padding-4{padding:4px}.mk-border-radius-4{border-radius:4px}.mk-hover:hover{background:var(--mk-ui-background-hover)}.mk-padding-12{padding:12px}.mk-layout-scroll{overflow:scroll}.mk-f-shadow{box-shadow:var(--shadow-x) var(--shadow-y) var(--shadow-blur) var(--shadow-spread) rgba(var(--shadow-color),calc(var(--shadow-alpha) / 100))}.mk-layout-row>.mk-f{width:0}.mk-layout-column{display:flex;flex-direction:column;align-items:flex-start}.mk-layout-grid{display:grid;grid-template-columns:repeat(var(--mk-grid-columns),minmax(var(--mk-grid-width),1fr))}.mk-layout-masonry{column-count:3}.mk-layout-masonry>*{width:100%;-webkit-column-break-inside:avoid;-o-column-break-inside:avoid;-moz-column-break-inside:avoid;break-inside:avoid}.mk-layout-row.mk-layout-align-nw,.mk-layout-row.mk-layout-align-n,.mk-layout-row.mk-layout-align-ne,.mk-layout-column.mk-layout-align-nw,.mk-layout-column.mk-layout-align-w,.mk-layout-column.mk-layout-align-sw{align-items:flex-start}.mk-layout-column.mk-layout-align-nw,.mk-layout-column.mk-layout-align-n,.mk-layout-column.mk-layout-align-ne,.mk-layout-row.mk-layout-align-nw,.mk-layout-row.mk-layout-align-w,.mk-layout-row.mk-layout-align-sw{justify-content:flex-start}.mk-layout-row.mk-layout-align-w,.mk-layout-row.mk-layout-align-m,.mk-layout-row.mk-layout-align-e,.mk-layout-column.mk-layout-align-n,.mk-layout-column.mk-layout-align-m,.mk-layout-column.mk-layout-align-s{align-items:center}.mk-layout-column.mk-layout-align-w,.mk-layout-column.mk-layout-align-m,.mk-layout-column.mk-layout-align-e,.mk-layout-row.mk-layout-align-n,.mk-layout-row.mk-layout-align-m,.mk-layout-row.mk-layout-align-s{justify-content:center}.mk-layout-row.mk-layout-align-sw,.mk-layout-row.mk-layout-align-s,.mk-layout-row.mk-layout-align-se,.mk-layout-column.mk-layout-align-ne,.mk-layout-column.mk-layout-align-e,.mk-layout-column.mk-layout-align-se{align-items:flex-end}.mk-layout-column.mk-layout-align-sw,.mk-layout-column.mk-layout-align-s,.mk-layout-column.mk-layout-align-se,.mk-layout-row.mk-layout-align-ne,.mk-layout-row.mk-layout-align-e,.mk-layout-row.mk-layout-align-se{justify-content:flex-end}.mk-layout-wrap{flex-wrap:wrap}.mk-layout-nowrap{flex-wrap:nowrap}.mk-masonry{column-count:attr(columns)}.mk-gap-4{gap:4px}.mk-gap-8{gap:8px}.mk-gap-16{gap:16px}.workspace-leaf-content img:not([width]).mk-node-image{max-width:unset}.mk-node-new{display:flex;align-items:center}.mk-frame-edit[data-path=main]+.mk-node-new{margin-bottom:100px}.mk-node-type{display:flex;padding:4px;border:thin solid var(--mk-ui-border);border-radius:4px;font-size:12px}.mk-node-image{object-fit:cover;max-width:100%!important}.mk-node-link{max-width:100%;display:flex;gap:4px;align-items:center;margin-bottom:4px}.mk-node-link .mk-path{padding:0}.mk-node-link:hover .mk-collapse{opacity:1}.mk-node-link .mk-collapse{opacity:0;width:24px!important;height:24px!important}.mk-node-link .mk-collapse svg{transform:rotate(90deg)}.mk-node-link .mk-collapse.mk-collapsed svg{transform:rotate(-90deg)}.mk-node-link .mk-collapse:hover{background:var(--mk-ui-background-hover)!important}.mk-frame-text{text-overflow:ellipsis;display:-webkit-box;overflow:hidden;-webkit-line-clamp:var(--line-count);-webkit-box-orient:vertical;color:inherit;font-size:var(--font-text-size);font-weight:var(--font-text-weight);font-style:var(--font-text-style);text-decoration:var(--font-text-decoration);color:var(--font-text-color);font-family:var(--font-text);white-space:pre-wrap}.mk-frame-text p{display:inline}.mk-frame-icon{display:flex;width:100%;height:100%}.mk-frame-icon svg{width:var(--icon-size);height:var(--icon-size)}.mk-frame-text[data-placeholder]:empty:before{content:attr(data-placeholder);color:var(--mk-ui-text-tertiary)}.mk-node-input[type=text]:active,.mk-node-input[type=text]:focus{box-shadow:none}.mk-button{-webkit-app-region:no-drag;display:inline-flex;align-items:center;justify-content:center;color:var(--mk-ui-text-primary);font-size:13px;border-radius:var(--button-radius);border:0;padding:4px var(--size-4-3);height:var(--input-height);font-weight:var(--input-font-weight);cursor:var(--cursor);font-family:inherit;outline:none;user-select:none;white-space:nowrap;background-color:var(--mk-ui-active-normal);box-shadow:var(--input-shadow)}.mk-node-image-placeholder{width:100%;height:100%;background-color:var(--mk-ui-active-normal);align-items:center;display:flex;justify-content:center}.mk-node-icon-placeholder{display:flex;color:var(--mk-ui-text-tertiary)}.mk-node-icon-placeholder svg{width:var(--icon-size);height:var(--icon-size)}.mk-frame-placeholder{display:flex;color:var(--mk-ui-text-tertiary);gap:4px;padding:0;font-weight:600;margin:4px 0}.mk-a{color:var(--link-color);outline:none;text-decoration-line:var(--link-decoration);text-decoration-thickness:var(--link-decoration-thickness);cursor:var(--cursor-link)}.mk-space-scroller{display:flex!important;flex-direction:column;align-items:flex-start!important;line-height:1.4;height:100%;position:relative;z-index:0;width:100%;overflow-x:hidden;padding:var(--file-margins);padding-bottom:100px}body.mk-readable-line{--page-width: var(--file-line-width)}body:not(.mk-readable-line){--page-width:100%}.mk-context-selector{font-size:13px;--tag-background: var(--background-secondary);position:relative;display:flex}.mk-context-header{margin:var(--file-margins);margin-bottom:0}body:not(.is-mobile) .mk-title-container .inline-title{width:100%}.is-mobile .mk-title-container .inline-title{flex:1}.mk-title-container{width:100%;display:flex;align-items:baseline;gap:4px;position:relative;flex-wrap:wrap}.mk-title-container .mk-title-prefix{display:flex;font-size:var(--inline-title-size);margin-top:8px;font-weight:var(--inline-title-weight);line-height:var(--inline-title-line-height)}.mk-title-container span{flex:1}.mk-title-container:hover .mk-title-alias,.mk-title-alias:hover{opacity:1}button.mk-title-alias{opacity:0;background:none;border:0;box-shadow:none;margin:0;height:100%;width:30px;padding:0!important;position:absolute;left:-30px;color:var(--mk-ui-text-tertiary)}.mk-title-alias svg{width:12px;height:12px;margin-top:8px}.mk-title-alias.mk-active{color:var(--mk-ui-active)}.mk-context-header{display:flex;flex-direction:column;max-width:var(--page-width);width:100%}.markdown-source-view.mod-cm6 .mk-space-body{max-width:100%;width:100%;margin-left:auto;margin-right:auto}.mk-space-title{position:relative}.mk-remote-header,.mk-space-header,.mk-space-footer{margin-left:auto;margin-right:auto;margin-bottom:8px;width:100%;max-width:var(--page-width);z-index:0;gap:8px;display:flex;flex-direction:column}body:not(.is-mobile) .markdown-source-view.mod-cm6 .mk-space-header .inline-title{flex-grow:1;margin-bottom:0;margin-left:0!important;margin-right:0;width:auto;max-width:inherit}.mk-space-sizer{display:flex;flex-direction:column;align-items:stretch;margin-left:auto;margin-right:auto}.mk-path-table-header{margin-top:24px;color:var(--mk-ui-text-tertiary)}.mk-path-table{border-collapse:collapse;table-layout:fixed;width:100%}.mk-space-body{position:relative;padding-bottom:100px}.mk-space-body>.mk-f{max-width:var(--page-width);margin:0 auto}.mk-frame[data-path=main],.mk-frame-edit[data-path=main]{gap:8px;margin-bottom:8px;display:flex;flex-direction:column;align-items:flex-start}.mk-frame[data-path=main]>.mk-layout-row,.mk-frame-edit[data-path=main]>.mk-layout-row{flex-wrap:wrap}.is-phone .mk-frame[data-path=main]>.mk-layout-row,.is-phone .mk-frame-edit[data-path=main]>.mk-layout-row{flex-wrap:wrap;flex-direction:column}.mk-frame[data-path=main]>div,.mk-frame-edit[data-path=main]>div,.mk-frame-edit[data-path=main]>input,.mk-frame-edit[data-path=main]+.mk-node-new{--max-width: var(--page-width);max-width:min(100%,var(--max-width))!important;margin-left:calc((max(100%,var(--max-width)) - var(--max-width)) /2)!important}.mk-frame-edit[data-path=main]>.mk-selected{background:var(--mk-ui-background-selected)}.mk-frame[data-path=main] .markdown-source-view.mod-cm6.is-readable-line-width .cm-content,.mk-frame-edit[data-path=main] .markdown-source-view.mod-cm6.is-readable-line-width .cm-content{--file-line-width: var(--max-width) !important}.mk-space-body .cm-content{padding-bottom:0!important}.mk-space-body>.mk-frame,.mk-space-body>.mk-frame-edit{align-items:flex-start;display:flex;flex-direction:column;gap:8px}.mk-path-row:hover{background:var(--mk-ui-background-hover)!important}.mk-path-table tr:nth-child(even){background:var(--color-base-10)}.mk-path-row td{padding:10px}.mk-path-row .mk-column-file{width:99%}.mk-path-row p{text-overflow:ellipsis;overflow:hidden;font-size:12px;color:var(--mk-ui-text-tertiary);margin:0;-webkit-line-clamp:2;-webkit-box-orient:vertical;display:-webkit-box}.mk-path-row .mk-path-name{font-weight:var(--font-medium)}.mk-path-date{font-size:12px;color:var(--mk-ui-text-secondary);width:100px}.mk-column-icon{width:40px}.mk-column-icon svg{width:16px;height:16px;color:var(--mk-ui-text-secondary)}.mk-flowspace-title svg{width:16px;height:16px;color:var(--mk-ui-text-secondary)}.mk-flowspace-title p{padding:0;margin:0 0 0 8px}.mk-flowspace-title .mk-flowspace-date{font-size:12px;color:var(--mk-ui-text-secondary)}.mk-flowspace-title{display:flex;align-items:center;padding:8px 12px;border-top:1px solid var(--mk-ui-divider)}.mk-flowspace-editor{padding:0 12px;width:100%}.mk-flowspace-editor:not(.mk-flow-node,.mk-path-context-flow) .mk-floweditor{padding:12px 0}.mk-frame-view .mk-flowspace-editor:not(.mk-flow-node,.mk-path-context-flow) .mk-floweditor{padding:0}.mk-flow-node .mk-floweditor .markdown-source-view.mod-cm6 .cm-sizer,.mk-flow-node .mk-floweditor .markdown-source-view.mod-cm6 .cm-content,.mk-flow-node .mk-floweditor .markdown-source-view.mod-cm6 .cm-line{max-width:100%}.mk-flow-node .cm-fold-indicator .collapse-indicator{right:0!important}.mk-flowspace-editor.mk-foldernote{width:var(--file-line-width);max-width:100%;margin-left:auto;margin-right:auto}.mk-foldernote .mk-floweditor{height:100%!important}.mk-flowspace-title span{flex-grow:1}.mk-flowspace-title button{padding:8px;margin-left:8px;width:unset}.mk-flowspace-title button.mk-open{background:var(--icon-color-active)}.mk-flowspace-title:hover{background:var(--color-base-10)}.mk-space-empty{width:100%;height:100%;display:flex;align-items:center;justify-content:center;font-size:13px;font-style:italic}.mk-space-scroller .mk-flowspace-editor{padding:0}.mk-context-type-selector{max-width:100%;width:100%;padding-top:4px;margin-left:auto;margin-right:auto}.mk-context-type-selector button{background:rgba(var(--nav-item-background-active),.3);border:none;box-shadow:none;color:var(--mk-ui-text-tertiary)}.mk-minimal-fix .mk-space-header,.mk-minimal-fix .mk-space-body{max-width:calc(100% - 60px)!important;margin:0 auto}.mk-minimal-fix .mk-floweditor .markdown-source-view.mod-cm6 .cm-contentContainer.cm-contentContainer>.cm-content>div{margin-inline:inherit!important;max-width:100%}.mk-minimal-fix .mk-floweditor-container .mk-floweditor .markdown-source-view.mod-cm6.is-readable-line-width .cm-line{padding:var(--embed-padding)!important;margin:0!important}.mk-space-view{height:100%}.mk-th{position:relative}.is-mobile .mk-resizer{width:8px;opacity:.05}.mk-resizer{position:absolute;right:0;top:0;height:100%;width:2px;background:var(--mk-ui-divider);cursor:col-resize;user-select:none;touch-action:none;opacity:0}.mk-resizer.mk-resizer-active{opacity:1}.mk-resizer.isResizing{background:var(var(--mk-ui-active-hover));opacity:1}.mk-resizer:hover{opacity:1}.mk-col-header{overflow-x:hidden;text-overflow:ellipsis;padding:4px;display:flex;align-items:center;font-size:13px;font-weight:var(--font-normal);color:var(--mk-ui-text-secondary);text-transform:capitalize}.mk-col-header>div{display:flex;gap:4px;width:100%;align-items:center}.mk-col-header-context{margin-left:4px;color:var(--mk-ui-text-tertiary)}.mk-td-aggregate{padding:.5rem;text-align:right}.mk-td-aggregate div{display:flex;width:100%;justify-content:flex-end;gap:8px}.mk-td-aggregate span{color:var(--mk-ui-text-tertiary);font-size:13px}.mk-td-aggregate.mk-empty div{opacity:0}.mk-td-aggregate:hover.mk-empty div{opacity:1}.mk-td{padding:.5rem;vertical-align:top}.mk-td>div{min-height:28px}.mk-td-group{background-color:var(--mk-ui-background);padding:.5rem;border-top:.5px solid var(--mk-ui-divider)!important;font-size:13px;font-weight:var(--bold-weight)}.mk-td-group .mk-cell-option{width:unset}.mk-td-group .mk-cell-option-item,.mk-td-group .mk-cell-option-item div:hover{background:none}.mk-td-empty{padding:0!important}.mk-td input[type=text],.mk-td input[type=number]{display:table-cell;width:100%;border:0;outline:0}.mk-table{width:100%;overflow-x:scroll}.mk-table table{border-spacing:0}.mk-table table th:last-child{width:100%}.mk-table th:hover{background:var(var(--mk-ui-active-hover))}.mk-table tr th:first-child .mk-col-header,.mk-table tr td:first-child{padding-left:calc(24px - .75rem)}.mk-cell-empty{color:var(--mk-ui-text-tertiary)}.mk-td img{max-height:45px}.mk-table tr td:first-child.mk-td-empty{padding-left:calc(24px - .75rem)!important}.mk-table td{border-top:.5px solid var(--background-modifier-border)!important;font-size:13px}.mk-table .mk-td:not(:last-child){border-right:.5px solid var(--background-modifier-border)!important}.mk-cell-object{display:flex;flex-direction:column;align-items:flex-start;gap:8px;width:100%}.mk-object-editor{display:flex;flex-direction:column;align-items:flex-start}.mk-cell-object-multi{display:flex;flex-direction:column;gap:8px}.mk-cell-object-options{display:flex;gap:8px}.mk-cell-object-group-header{display:flex;gap:8px;font-size:14px;font-weight:var(--bold-weight);color:var(--mk-ui-text-tertiary);margin-top:8px;margin-bottom:4px}.mk-cell-object-group{display:flex;align-items:flex-start;flex-direction:column}.mk-cell-object .mk-path-context-field-key{width:100%;border:none;border-radius:0;height:24px}.mk-cell-object .mk-path-context-field-key:not(:focus){background:unset!important}.mk-cell-object-row{display:flex}.mk-cell-flex{display:flex;gap:8px;flex:1}.mk-cell-flex .mk-cell-number,.mk-cell-flex .mk-cell-boolean{flex:1}.mk-path-context-value>.mk-cell-flex,.mk-cell-flex:has(.mk-cell-number){flex-direction:row-reverse;justify-content:flex-end}.mk-cell-flex>.mk-icon-small{visibility:hidden;color:var(--mk-ui-text-secondary);padding:4px}.mk-cell-flex:hover>.mk-icon-small{visibility:visible}.mk-path-context-value>.mk-cell-flex>.mk-icon-small{visibility:visible}.mk-path-context-value .mk-cell-number{text-align:left}.mk-cell-flex:hover>.mk-icon-small:hover{background:var(--mk-ui-background-hover);border-radius:4px}.mk-cell-aggregate,.mk-cell-formula{flex:1}.mk-cell-text{white-space:pre-line;width:100%;min-height:24px;padding:4px}.mk-cell-text:empty:before{content:attr(data-ph);color:var(--mk-ui-text-tertiary)}.mk-table .mk-cell-text{padding:4px}.mk-cell-number{padding:4px;text-align:right}.mk-table .mk-cell-text:not(:focus){background:unset!important}.mk-cell-image-item:hover>.mk-image-selector{visibility:visible}.mk-cell-image{color:var(--mk-ui-text-tertiary);position:relative;height:100%;display:flex;gap:8px}.mk-cell-image img{min-width:60px;min-height:60px}.mk-path-context-value .mk-cell-image img{height:60px}.mk-button-new{background:var(--mk-ui-active)!important;color:var(--text-on-accent)!important}.mk-button-new svg{width:var(--icon-size);height:var(--icon-size)}.mk-image-selector{position:absolute;visibility:hidden;top:0;left:0;z-index:var(--layer-popover);padding:4px;display:flex;gap:6px}.mk-cell-image img{max-height:100%;overflow:hidden;border-radius:4px}.mk-cell-date{width:100%;min-height:30px;display:flex;align-items:center}.mk-cell-date .mk-cell-date-new{color:var(--mk-ui-text-tertiary)}.mk-cell-date .mk-cell-date-item{background:rgba(var(--mono-rgb-100),.025);border-radius:4px;overflow:hidden;display:flex;align-items:center;gap:4px;white-space:nowrap;height:24px;-webkit-line-clamp:1;line-clamp:1;-webkit-box-orient:vertical;padding:2px 4px}.mk-cell-date-item:hover{background:var(--mk-ui-active-hover)}.mk-cell-link,.mk-cell-context,.mk-cell-tags,.mk-cell-option{width:100%;min-height:24px;gap:4px;display:flex;flex-wrap:wrap;overflow:hidden}.mk-cell-context .mk-path,.mk-cell-link .mk-path{background:var(--mk-color-none)}.mk-cell-context .mk-path:hover,.mk-cell-link .mk-path:hover{background:var(--mk-ui-background-hover)}.mk-celllink .mk-path span{max-width:100px}.mk-cell-boolean{padding:4px;display:flex;align-items:center;gap:2px}.mk-td .mk-cell-option-item{max-width:120px}.mk-cell-space{display:flex;gap:8px}.mk-cell-clickable{background:rgba(var(--mono-rgb-100),.025);border-radius:4px;overflow:hidden;display:flex;align-items:center;gap:4px;white-space:nowrap;height:24px;-webkit-line-clamp:1;line-clamp:1;-webkit-box-orient:vertical;padding:2px 4px}.mk-cell-tags-label,.mk-cell-option-item{background:rgba(var(--mono-rgb-100),.025);border-radius:4px;overflow:hidden;display:flex;align-items:center;gap:2px;white-space:nowrap;height:24px;padding:2px 4px;-webkit-line-clamp:1;line-clamp:1;-webkit-box-orient:vertical}.mk-cell-option-item.mk-active{background:var(--mk-ui-active)!important}.mk-cell-option-item input{margin:0}.mk-cell-icon>div{display:flex;width:24px;height:24px;background:rgba(var(--mono-rgb-100),.025)}.mk-cell-icon .mk-cell-placeholder{width:auto}.mk-cell-option-item:hover{background:var(--mk-ui-active-hover)}.mk-cell-fileprop{padding:4px;color:var(--mk-ui-text-secondary);width:100%}.mk-cell-file{display:flex;align-items:center}.mk-cell-file-title{font-size:15px;margin-bottom:8px}.mk-cell-file-new{display:flex;align-items:center}.mk-cell-file-new .mk-path-icon svg{color:var(--mk-ui-text-tertiary)}.mk-table:focus .mk-active{background:var(--mk-ui-background-selected)!important}.mk-floweditor .mk-table:not(:focus) .mk-active{background:none!important}.mk-table:focus .mk-active .mk-selected-cell{outline:solid 2px var(--mk-ui-active)}.mk-cell-file-new .mk-path-icon:hover{background:var(var(--mk-ui-active-hover))}.mk-cell-file-item{display:flex;align-items:center}.mk-cell-file-item .mk-path-icon:hover{background:var(var(--mk-ui-active-hover))}.mk-cell-file-item .mk-cell-file-name{padding:2px 4px;border-radius:4px}.mk-cell-file-item .mk-cell-file-name:hover{background:var(var(--mk-ui-active-hover))}.mk-cell-file-new .mk-cell-file-name{padding:4px!important;background:none!important}.is-phone .mk-cell-file-flow{display:none}.mk-list-item .mk-cell-file-flow{visibility:hidden;margin-left:4px;padding:2px 4px}.mk-list-item:hover .mk-cell-file-flow{visibility:visible}.is-phone .mk-list-item .mk-cell-file-flow{width:unset;visibility:unset}.mk-list-item .mk-cell-file-flow:hover{border-radius:4px;background:var(var(--mk-ui-active-hover))}.mk-row-new[data-placeholder]:empty:before{content:attr(data-placeholder);color:var(--mk-ui-text-tertiary)}.mk-row-new{text-align:left;border-top:.5px solid var(--mk-ui-divider)!important;padding:8px 24px!important;font-weight:400!important;color:var(--mk-ui-text-tertiary)!important;font-size:13px!important}.markdown-rendered .mk-table td,.markdown-rendered .mk-table th{border:unset}.mk-row-new:hover{background:var(--mk-ui-background-hover)}.mk-icon-medium svg{width:24px;height:24px;color:var(--mk-ui-text-primary)}.mk-icon-small svg{width:16px;height:16px;stroke:var(--mk-ui-text-secondary)}.mk-icon-medium,.mk-icon-small,.mk-icon-xsmall{display:flex;align-items:center}.mk-icon-xxsmall svg{width:10px;height:10px;color:var(--mk-ui-text-secondary)}.mk-icon-xsmall svg{width:12px!important;height:12px!important}.mk-icon-rotated svg{transform:rotate(90deg)}.mk-cell-link-item{color:var(--link-color);display:flex}.mk-cell-option span{flex-grow:1;overflow:hidden}.mk-cell-option-placeholder{color:var(--mk-ui-text-tertiary)}.mk-cell-option-select,.mk-cell-option-remove{display:flex;align-items:center;padding:4px;border-radius:4px}.mk-cell-option-select svg,.mk-cell-option-remove svg{color:var(--mk-ui-text-primary)}.mk-cell-option-select:hover,.mk-cell-option-remove:hover,.mk-cell-clickable:hover{background:var(--mk-ui-background-hover)}.mk-cell-option-new{visibility:hidden;background:rgba(var(--mono-rgb-100),.025);padding:4px;display:flex;border-radius:4px;height:24px;width:24px}.mk-cell-option-new:hover{background:var(--mk-ui-background-hover)}.mk-cell-tags:hover .mk-cell-option-new,.mk-cell-link:hover .mk-cell-option-new,.mk-cell-context:hover .mk-cell-option-new,.mk-cell-option:hover .mk-cell-option-new{visibility:visible}.mk-cell-link-unresolved,.mk-cell-link-unresolved:hover{color:var(--link-unresolved-color);opacity:var(--link-unresolved-opacity);filter:var(--link-unresolved-filter)}.mk-t-h1{--font-text-size: var(--h1-size);--text-normal: var(--h1-color);--font-weight: var(--h1-weight)}.mk-t-h2{--font-text-size: var(--h2-size);--text-normal: var(--h2-color);--font-weight: var(--h2-weight)}.mk-t-h3{--font-text-size: var(--h3-size);--text-normal: var(--h3-color);--font-weight: var(--h3-weight)}.mk-t-h4{--font-text-size: var(--h4-size);--text-normal: var(--h4-color);--font-weight: var(--h4-weight)}.mk-t-h5{--font-text-size: var(--h5-size);--text-normal: var(--h5-color);--font-weight: var(--h5-weight)}.is-phone .mk-inline-button{width:unset!important}.is-tablet .mk-inline-button{padding:unset!important}.mk-inline-button{display:inline-flex;align-items:center;justify-content:center;padding:4px;border-radius:var(--clickable-icon-radius);box-shadow:unset!important}.mk-inline-button:not(:hover){background:unset!important}.mk-inline-button{font-size:13px;display:flex;align-items:center;gap:10px;width:auto!important}.mk-inline-button{background:none!important;box-shadow:none!important;color:var(--mk-ui-text-tertiary)!important;padding:0 4px!important;display:flex;gap:4px;height:20px;border:none!important}.mk-inline-button svg{color:var(--mk-ui-text-tertiary)!important}body:not(.is-mobile) .mk-inline-button:hover svg,body:not(.is-mobile) .mk-inline-button:hover{color:var(--mk-ui-text-primary)!important}.mk-inline-button{background:none;border:0;box-shadow:none;margin:0;height:24px;width:24px;padding:0!important}
